{"level":"dev.info","ts":"[2025-08-30 09:39:00.040]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"30.4845ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.116]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"106.0986ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.165]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"47.7027ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-30 09:39:11.885]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"19.0926ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:11.904]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"19.4975ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"24.1763ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.138]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"36.3391ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.220]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"81.2283ms","duration_ms":81}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.248]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"26.1868ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.271]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"22.9324ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.301]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"28.3893ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"34.5045ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.355]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 461]","duration":"18.8412ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.375]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"20.3212ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.395]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"19.646ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.423]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"28.3047ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.436]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"12.8386ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.460]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"24.127ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.479]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"18.6668ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.510]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"30.6145ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"21.823ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.564]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"31.8728ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.594]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"29.6967ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.615]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"21.4997ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.644]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"28.4906ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.670]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"26.0079ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.693]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"22.4932ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.745]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"51.7106ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.764]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"18.5698ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.789]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"25.5319ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.806]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"17.0454ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.866]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"59.3804ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.886]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"20.2857ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.913]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"25.0909ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.933]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"20.6955ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:39:12.988]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"54.7513ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"22.6383ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"78.0959ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.149]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"59.368ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.183]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"33.021ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.221]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"33.3756ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.257]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"35.9146ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.272]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"15.3149ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.314]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"39.6815ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.347]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"33.1973ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.379]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"31.8015ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.413]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"33.9815ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.435]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"21.9808ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.456]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"42.014ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.456]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"41.3014ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"62.5796ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"62.5796ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.529]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-30]","duration":"111.3478ms","duration_ms":111}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.530]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"113.5503ms","duration_ms":113}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.530]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"113.5503ms","duration_ms":113}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.530]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-30 2025-08-30]","duration":"114.9604ms","duration_ms":114}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.530]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"113.5503ms","duration_ms":113}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-30 2025-08-30]","duration":"114.9535ms","duration_ms":114}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.532]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"114.9603ms","duration_ms":114}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.536]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"120.4151ms","duration_ms":120}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.552]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"14.9282ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.554]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-30 2025-08-30]","duration":"22.193ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.554]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-30 2025-08-30]","duration":"23.2356ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.554]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-30]","duration":"24.2919ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.570]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c0ff4f573","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-30 2025-08-30]","duration":"13.9744ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.572]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c9e507302","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"18.9594ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.595]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-30 2025-08-30]","duration":"23.7109ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.596]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"21.8762ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:19.779]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"34.4873ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:39:19.803]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"22.7482ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:19.863]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"19.3894ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:19.925]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"61.3114ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-30 09:39:19.987]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"60.1028ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.009]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"21.559ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"49.6593ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.083]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"23.7194ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"29.4963ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.155]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 461]","duration":"42.7515ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"180.6603ms","duration_ms":180}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.366]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"29.9142ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"35.9931ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.425]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"21.9278ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.458]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"33.4635ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.472]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"14.3503ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.506]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"32.5981ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"67.5281ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.595]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"21.7144ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.636]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"41.0899ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.697]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"61.0182ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.729]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"31.849ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.754]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"25.2183ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.785]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"31.2137ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.816]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"31.1011ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.844]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"27.8738ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.868]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"23.9786ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.888]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"19.4377ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.926]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"38.0108ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.951]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"25.0038ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:39:20.979]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"27.4157ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"33.1083ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"54.4526ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.085]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"18.3258ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"22.5559ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.126]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"18.4274ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"16.9694ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.192]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"49.0259ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.218]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"26.2566ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.237]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"18.3789ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.276]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"39.4164ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.295]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"18.0955ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.314]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"18.6606ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"21.3677ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.358]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c5ea2d4a0c71a63e32","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"22.0884ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.597]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"36.4538ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.599]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"37.822ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.600]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-30 2025-08-30]","duration":"38.3064ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"40.4926ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"39.9731ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"39.9731ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.615]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-08-30 2025-08-30]","duration":"53.5643ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.615]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"54.6093ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.763]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-30]","duration":"201.1393ms","duration_ms":201}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.763]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"201.8073ms","duration_ms":201}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"174.6939ms","duration_ms":174}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-08-30 3 9 2]","duration":"174.6939ms","duration_ms":174}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-08-30 2025-08-30]","duration":"172.6445ms","duration_ms":172}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.773]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-30 2025-08-30]","duration":"157.9066ms","duration_ms":157}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.782]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-08-30]","duration":"19.7927ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.783]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"19.7541ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.792]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-08-30 2025-08-30]","duration":"18.8074ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.804]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c79465b10c093053cb","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"20.8686ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.810]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-08-30 2025-08-30]","duration":"17.0223ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.840]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE `id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) LIMIT 1, [60 61 62 63 64 65 66 67 68 69 70 73 75 76 77 78 79 80 81 82 83 84 85 86 87 89 88]","duration":"35.0439ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.639]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"78.2408ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.640]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"78.2004ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.703]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"63.348ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.730]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"26.7356ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.862]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"130.931ms","duration_ms":130}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.943]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c7946d6a0055a9268c","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"80.5753ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-08-30 09:39:28.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"61.6623ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.832]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"12.8362098s","duration_ms":12836}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.832]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"12.8362098s","duration_ms":12836}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.885]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"52.7889ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.885]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"51.7354ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.902]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"68.805ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.902]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"69.3312ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.902]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"69.9088ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.909]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"23.1426ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.909]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c0741fd32bf3","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"23.7349ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c07409bd85f8","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"29.1301ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.940]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067c913f9c07409bd85f8","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"30.9111ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"157.6899ms","duration_ms":157}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"168.5375ms","duration_ms":168}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.154]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"114.016ms","duration_ms":114}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.162]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"63.0195ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"\n\t\tSELECT name\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"18.7006ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.188]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"\n\t\tSELECT updated_at\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ? AND risk_result = 0\n\t\tORDER BY updated_at DESC\n\t\tLIMIT 1\n\t, [7]","duration":"15.5762ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.205]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067cc1d6bfae8004e403b","sql":"\n\t\tSELECT name\n\t\tFROM business_account\n\t\tWHERE id = ?\n\t, [1]","duration":"16.6895ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.421]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"39.8605ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.475]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"54.3569ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.513]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"37.0621ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.571]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"58.4669ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"30.2997ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.288]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"106.8207ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.331]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"42.7455ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.367]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"35.8025ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.367]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"184.9699ms","duration_ms":184}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"195.1961ms","duration_ms":195}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.385]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"16.6972ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.385]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"17.8477ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.409]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"24.5024ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.418]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"49.9168ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.418]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"49.9168ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.446]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e584d5a46eb","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"16.1233ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.476]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"29.6066ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.478]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"31.3304ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.478]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"31.8447ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.479]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"32.7733ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.479]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"32.259ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.498]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"19.751ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:40:21.803]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"12.2942669s","duration_ms":12294}
{"level":"dev.info","ts":"[2025-08-30 09:40:21.803]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"12.2952963s","duration_ms":12295}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.568]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"7.762093s","duration_ms":7762}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.599]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2b9a888f847de5f32","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"29.8524ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"19.5119ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"20.0165ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.637]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"18.6678ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.638]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067d2aaec4e5868cfff03","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"19.1735ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:00.076]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"67.7395ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"35.7573ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.169]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"27.1728ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.337]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"29.0117ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.357]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"19.0283ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.388]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"30.8185ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.404]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"16.3183ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.420]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"15.7171ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.458]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"38.203ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.485]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"27.3181ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.507]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 461]","duration":"22.1273ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.526]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"18.3605ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"18.4732ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.562]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"18.1785ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.581]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"18.5348ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.628]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"46.1722ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.684]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"55.3724ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.696]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"12.4031ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.717]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"20.3891ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.730]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"13.1168ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.751]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"21.0128ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"18.2923ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.788]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"18.0849ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.813]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"25.6136ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.831]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"15.705ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.868]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"36.8152ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.887]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"19.8174ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.906]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"18.67ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.923]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"16.9434ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.944]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"20.4804ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.965]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"19.8162ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.986]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"20.439ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.003]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"17.3548ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"13.2489ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.050]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"33.0837ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"16.3328ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"23.2958ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"18.0342ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.125]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"17.1685ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"18.2344ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.156]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"13.1559ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.178]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"21.5976ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.201]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"23.1221ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.215]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"14.2242ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.233]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"17.076ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.252]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"19.0999ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad47d73796c","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"15.6723ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad47d73796c","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"15.6723ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad47d73796c","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"15.6723ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.789]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad47d73796c","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"18.614ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.789]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad47d73796c","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"18.614ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.799]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"28.191ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.799]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"28.7011ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.808]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"19.1606ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.815]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"16.2916ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.831]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"22.633ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.857]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"40.6406ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.885]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"53.6659ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.943]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e04e12aad4b6562c7e","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"23.3171ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"14.8879ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"14.8879ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"14.8879ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"14.8879ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"83.6363ms","duration_ms":83}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"19.2159ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.076]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"21.6185ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.076]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"21.6185ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.093]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"16.7497ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.120]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"17.2379ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"13.4074ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.173]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"13.9364ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"34.6187ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"33.792ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.221]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e057e1e69c22a07e28","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"13.6915ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.308]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"14.0218ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.336]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"\n\t\tSELECT name\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"28.2726ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.357]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"\n\t\tSELECT updated_at\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ? AND risk_result = 0\n\t\tORDER BY updated_at DESC\n\t\tLIMIT 1\n\t, [7]","duration":"21.1368ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"\n\t\tSELECT name\n\t\tFROM business_account\n\t\tWHERE id = ?\n\t, [1]","duration":"20.2514ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.812]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"12.926ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.832]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"19.1415ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.876]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"15.9188ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.900]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"24.8879ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.914]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"12.9608ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.942]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"28.0065ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.958]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"15.1982ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.991]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"32.7754ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.005]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"13.8589ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 461]","duration":"20.0409ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.040]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"14.9649ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"18.6441ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.081]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"21.1505ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"20.9486ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.128]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"26.4791ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.150]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"21.7959ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.168]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"18.1532ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"15.3737ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.204]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"20.7956ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.222]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"17.9656ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.243]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"20.9137ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"15.7255ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.287]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"27.6575ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"16.0331ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.321]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"18.1875ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.339]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"16.7468ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.358]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"18.686ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.376]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"17.8295ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.437]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"60.6149ms","duration_ms":60}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.470]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"33.1933ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.487]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"15.6343ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"32.015ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"19.1485ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.588]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"49.1226ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.607]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"17.6205ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.625]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"18.6803ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.649]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"24.0521ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.677]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"27.2253ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"13.7088ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.709]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"18.8629ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.726]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"16.6556ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.745]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"19.1395ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.778]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"32.8451ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.799]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"21.6596ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.818]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e06e3dc5f0f11f4dc9","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"18.7741ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.167]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"26.7516ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.193]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a549860ab3e","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"52.1554ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.193]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a549860ab3e","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"52.1554ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.196]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a549860ab3e","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"9.6669ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.206]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"11.6336ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.206]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"11.6336ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.236]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"38.1335ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.350]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"144.5535ms","duration_ms":144}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.359]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"8.7407ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.376]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"176.5147ms","duration_ms":176}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.385]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"26.1107ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.425]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"39.7698ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e18f177a54b47981cb","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"29.7142ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.488]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"18.1756ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.489]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"19.5375ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.489]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"19.2365ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.489]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"19.2365ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.499]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"10.591ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.541]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"71.2338ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.596]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"43.1759ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.605]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"52.3139ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.632]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"27.1887ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.662]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"23.1647ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.707]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"21.9313ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.710]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"22.7381ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.720]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"12.8527ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.729]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"18.7785ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.747]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1a0dbbb101ce19ce4","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"16.9792ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.802]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"19.6698ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.826]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"\n\t\tSELECT name\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"23.3916ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.846]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"\n\t\tSELECT updated_at\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ? AND risk_result = 0\n\t\tORDER BY updated_at DESC\n\t\tLIMIT 1\n\t, [7]","duration":"20.0307ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.862]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"\n\t\tSELECT name\n\t\tFROM business_account\n\t\tWHERE id = ?\n\t, [1]","duration":"16.5188ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.362]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"14.478ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"16.1935ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.424]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"16.4103ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.439]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"15.2183ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.473]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"33.5586ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.486]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"12.5389ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.501]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"14.8504ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.517]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"16.0049ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.533]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"15.3413ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.550]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 461]","duration":"17.2732ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.568]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"18.1793ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.594]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"25.1526ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.611]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"17.5971ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.636]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"24.9686ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.669]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"32.2168ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"20.8066ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.713]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"23.0909ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.732]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"18.9613ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.757]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"25.0753ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.783]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"26.0154ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.800]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"17.1667ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.820]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"18.8844ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"17.8395ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.865]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"26.0988ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.884]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"18.1238ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.914]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"30.3582ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.933]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"18.5472ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.953]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"20.2243ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.997]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"43.0839ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.023]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"26.209ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.270]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"245.3053ms","duration_ms":245}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.298]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"28.3457ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.319]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"20.5497ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.347]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"28.2055ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"30.179ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.419]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"40.8382ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.451]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"31.9485ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.485]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"33.1094ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.504]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"19.1251ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.534]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"30.5427ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"39.1664ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.605]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"31.9822ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.629]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"23.8118ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.663]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"34.2441ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.698]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e1b55c1724233486b3","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"34.4181ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.006]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"18.0172ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.006]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"17.5131ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.006]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"17.5131ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.026]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2eba78da8f4f9c1fd","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"19.8142ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2eba78da8f4f9c1fd","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"21.3257ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2eba78da8f4f9c1fd","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"21.8337ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2eba78da8f4f9c1fd","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"22.054ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.049]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"20.7259ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"11.5737ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.059]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"9.8646ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"9.1072ms","duration_ms":9}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"12.2271ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.122]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2ebaf3ecc5c4428de","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"23.6887ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.141]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"18.2204ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"28.3646ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.151]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"28.5839ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.164]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"41.1932ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.164]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"41.1932ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.197]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"45.8918ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.250]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"29.561ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.251]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"31.1171ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.269]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"17.5895ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.296]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"15.6409ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.357]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"42.4543ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"51.9092ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.389]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"19.4318ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.398]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"18.5501ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.420]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e2f2368804557f2a1f","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"21.8635ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.467]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"13.9245ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.497]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"\n\t\tSELECT name\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"29.313ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.521]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"\n\t\tSELECT updated_at\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ? AND risk_result = 0\n\t\tORDER BY updated_at DESC\n\t\tLIMIT 1\n\t, [7]","duration":"23.9587ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.545]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"\n\t\tSELECT name\n\t\tFROM business_account\n\t\tWHERE id = ?\n\t, [1]","duration":"24.1231ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"24.4449ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.262]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"13.0643ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"27.4078ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.347]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"18.7338ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.371]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"23.5327ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.405]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"33.624ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.431]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"26.3044ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.449]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"17.8271ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.516]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"67.2018ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.602]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 461]","duration":"85.5799ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.626]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"23.4588ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.643]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"17.2848ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.673]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"29.537ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.687]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"13.8728ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.714]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"27.7411ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.740]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"25.9316ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.772]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"31.4158ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.800]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"28.6009ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.853]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"52.8011ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.886]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"33.2269ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.946]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"59.3223ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.978]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"31.8427ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.001]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"22.947ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"18.3427ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"40.4462ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.096]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"35.9181ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"21.6656ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"25.1649ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.160]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"16.1611ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.179]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"18.9948ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.199]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"19.9715ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.253]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"53.2863ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.275]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"22.0627ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.298]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"22.091ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.329]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"31.6457ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.359]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"30.2317ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.377]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"17.4596ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.391]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"13.5341ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.416]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"24.9947ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.439]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"22.9796ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.460]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"20.495ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.479]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"18.8557ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"39.8214ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"18.9134ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.561]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e3075f8dd42d335522","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"23.1233ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.938]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"25.0868ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.938]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"25.0868ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64e6233c1b","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"26.0911ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:41:35.837]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64e6233c1b","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"4.8948408s","duration_ms":4894}
{"level":"dev.info","ts":"[2025-08-30 09:41:35.835]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64e6233c1b","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"4.8968987s","duration_ms":4896}
{"level":"dev.info","ts":"[2025-08-30 09:41:35.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64e6233c1b","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"4.8979243s","duration_ms":4897}
{"level":"dev.info","ts":"[2025-08-30 09:41:35.840]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64e6233c1b","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"4.9025789s","duration_ms":4902}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.216]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"1.3755105s","duration_ms":1375}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.229]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"12.922ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.230]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"15.0223ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.251]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"21.3775ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.251]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"21.3789ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.296]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e5b266bc64f6c7632a","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"20.5676ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.321]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"24.8652ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"25.8691ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"25.8691ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"25.8691ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"25.8691ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.335]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"12.7324ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.360]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"17.4841ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.367]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"24.0549ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.403]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"35.807ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.438]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"18.7969ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.503]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"23.6857ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.506]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? ORDER BY evaluation_time DESC, [7]","duration":"24.9575ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.514]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"11.4222ms","duration_ms":11}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.533]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"25.4728ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e72da440305dfa2ef9","sql":"SELECT * FROM `risk_raw_data` WHERE `evaluation_id` = ? LIMIT 1, [EVAL_7_1756110858976228500]","duration":"10.3655ms","duration_ms":10}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.629]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e741af60f0e4995430","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"17.3796ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.642]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e741af60f0e4995430","sql":"\n\t\tSELECT name\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"13.1879ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.651]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e741af60f0e4995430","sql":"\n\t\tSELECT updated_at\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ? AND risk_result = 0\n\t\tORDER BY updated_at DESC\n\t\tLIMIT 1\n\t, [7]","duration":"8.3674ms","duration_ms":8}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.670]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186067e741af60f0e4995430","sql":"\n\t\tSELECT name\n\t\tFROM business_account\n\t\tWHERE id = ?\n\t, [1]","duration":"19.0495ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.691]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"18.1257ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.745]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"30.3802ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.762]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"15.809ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.812]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"95.0552ms","duration_ms":95}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de63062eff139","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"102.0796ms","duration_ms":102}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.821]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de63062eff139","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"59.3307ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.861]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de63062eff139","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"47.6016ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-08-30 09:51:12.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de63062eff139","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"229.3002ms","duration_ms":229}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.677]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"2.6259484s","duration_ms":2625}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.678]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"2.8653837s","duration_ms":2865}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.678]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"2.8653837s","duration_ms":2865}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.700]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"66.0714ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.714]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686ced0de630dc10c92a","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"18.2485ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.720]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"19.561ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.739]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"24.513ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.739]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"24.513ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.740]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"25.5721ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.740]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"25.5721ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.815]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"100.6733ms","duration_ms":100}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.851]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"36.3679ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.958]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"93.6135ms","duration_ms":93}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.981]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"117.1359ms","duration_ms":117}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.989]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"31.0487ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:51:15.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"23.0486ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:51:15.055]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"20.4857ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:51:15.110]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860686d9e976610dc644f9b","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"54.9482ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095200","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"21.0159ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.028]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095200","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"24.3548ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.029]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095200","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"25.1063ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"35.8738ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.114]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"48.0169ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.183]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"68.6252ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.190]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"123.721ms","duration_ms":123}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"2.623325s","duration_ms":2623}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"2.5065003s","duration_ms":2506}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.724]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"33.9726ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.724]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"34.5021ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.725]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"34.4775ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.744]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"19.4099ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.754]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"63.176ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.756]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"31.1735ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.763]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"72.5597ms","duration_ms":72}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.783]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068835beab8602ca50f64","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"26.2345ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"27.4482ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"28.0954ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"27.5891ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.812]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"28.0988ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.812]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"28.4688ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.831]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"19.5374ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.887]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"40.5422ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.888]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"41.0475ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.907]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"19.3376ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.937]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"16.0518ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.968]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"16.585ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.985]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606883fc395718d3861475","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"16.7982ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:53:00.024]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"23.8697ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:53:33.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"37.013ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.875]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c3474ba118e","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"27.3291ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.894]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c3474ba118e","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"45.8665ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.894]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c3474ba118e","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"48.6353ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.930]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c3474ba118e","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"54.9274ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.931]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c3474ba118e","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"55.4964ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.431]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"7.5367699s","duration_ms":7536}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.595]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"163.4842ms","duration_ms":163}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.634]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"7.7574713s","duration_ms":7757}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.634]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"43.5049ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.764]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"169.3396ms","duration_ms":169}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.775]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"140.643ms","duration_ms":140}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.875]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"99.2675ms","duration_ms":99}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.903]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860688e7bf02c34a13fd35b","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"15.497ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.925]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"22.4076ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.935]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"32.4486ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.935]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"32.4486ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.935]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"32.4486ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.944]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"41.0993ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.952]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"27.1653ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.001]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"39.9248ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.001]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"39.9248ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"14.0999ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"27.2754ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.114]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"44.3469ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"18.0135ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.557]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068905b1b11fc28f1ba4b","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"46.1374ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.623]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"41.6892ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.657]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"32.9679ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.665]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"82.3453ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.689]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea31cdf822a2fe82","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"31.7766ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.689]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea31cdf822a2fe82","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"104.6943ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.693]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea31cdf822a2fe82","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"27.5308ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.724]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea31cdf822a2fe82","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"35.1783ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"45.2827ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"103.9895ms","duration_ms":103}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.780]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"114.8049ms","duration_ms":114}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.780]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"36.6639ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.804]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"23.4374ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.809]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891ea435578a7a9d03c","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"17.2993ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"28.2164ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"28.7286ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"28.2164ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"28.2164ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.846]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"36.6024ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.856]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"18.1719ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.886]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"22.8646ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.905]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"42.0409ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"32.9209ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.966]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"17.5855ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.998]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"16.1004ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:53:51.018]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"19.7592ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606891f68f2618fedc5a33","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"34.5872ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.657]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb8e5d02f7d","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"15.7391ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.666]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb8e5d02f7d","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"21.3081ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.666]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb8e5d02f7d","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"21.3081ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.694]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb8e5d02f7d","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"27.2877ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.694]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb8e5d02f7d","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"35.8925ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.703]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"36.4198ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.703]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"36.4198ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.713]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"19.2889ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.721]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"18.3011ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.727]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"14.3478ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.739]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"17.8028ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.749]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"22.1527ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.785]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606892650dedb88473c34f","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"18.6673ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.822]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"37.3696ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.823]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"38.3776ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.824]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"39.403ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.824]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"39.403ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.825]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"39.403ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.843]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"19.9284ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.873]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"21.0742ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.891]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"38.8405ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.936]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"44.3991ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.994]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"48.4264ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-30 09:53:53.024]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"15.9059ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:53:53.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"32.7871ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.940]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068926c58930c7c3b8734","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"53.1682ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.992]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"19.9098ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.997]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"21.812ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.001]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"26.3657ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.012]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de4f31cc9c08e1be","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"20.4491ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de4f31cc9c08e1be","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"15.3826ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de4f31cc9c08e1be","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"15.3826ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de4f31cc9c08e1be","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"15.3826ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.036]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"23.5148ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.045]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"19.1826ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.060]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"23.5894ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.094]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"48.2517ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"70.866ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.167]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893de5ea8006affad1b","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"21.2331ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"17.7114ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"17.1992ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.184]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"17.1992ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.185]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"18.1474ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.197]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"30.0774ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.206]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"19.7798ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.276]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"58.217ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.282]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"64.1457ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.337]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"53.6418ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.369]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"21.058ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.395]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"14.3991ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.420]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606893e8948d080cca4436","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"24.9599ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095400","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"68.0861ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095400","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"62.3301ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095400","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"76.0819ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250830095400","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"68.6093ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"14.0038ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"30.627ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"16.299ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.126]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"32.4994ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"31.8158ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945ca88b2c79f7191a","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"34.1121ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.146]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945ca88b2c79f7191a","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"19.1292ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.146]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945ca88b2c79f7191a","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"19.2706ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.193]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945ca88b2c79f7191a","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"66.7305ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.211]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"18.4606ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.230]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"21.9217ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.250]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"38.873ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.255]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"22.7105ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"13.7273ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.294]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068945cc2820ccee38a39","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"17.6554ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.313]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"19.7337ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.314]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"20.3248ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.324]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"30.5902ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.325]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"31.1058ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.325]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"31.1058ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.339]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"24.5912ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.373]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"25.879ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.405]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"58.3537ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.434]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"28.6511ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.463]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"17.3982ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.492]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"17.4021ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.517]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"25.1283ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.096]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606894679241cccc229e70","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"41.9297ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.205]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"84.8111ms","duration_ms":84}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.212]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954caf4bec9f4fdc25","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"94.2066ms","duration_ms":94}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.214]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954caf4bec9f4fdc25","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"93.8681ms","duration_ms":93}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.238]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954caf4bec9f4fdc25","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"32.4692ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.238]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954caf4bec9f4fdc25","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"32.4692ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.248]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954caf4bec9f4fdc25","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"41.9397ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.248]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954caf4bec9f4fdc25","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"33.4866ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.274]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"17.5143ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.274]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"26.3934ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.294]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"16.288ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"28.6889ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.313]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"19.0822ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186068954cb6fea06ddfdb44","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"46.5532ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.389]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"17.2388ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.389]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"17.7583ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.389]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"17.2388ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.490]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"118.0425ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.490]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"118.562ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"130.057ms","duration_ms":130}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.571]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"38.0856ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.576]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"42.7512ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.639]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"62.5684ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.681]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"32.212ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.788]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"94.8533ms","duration_ms":94}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.836]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"48.4708ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.231]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689558ea91004ba62697","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"42.1745ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.285]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"29.561ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.305]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"46.7713ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.305]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"46.7713ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.316]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a8746817bd","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"30.2821ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a8746817bd","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"16.5231ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a8746817bd","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"16.5231ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.323]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a8746817bd","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"17.0634ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.330]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a8746817bd","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"14.5108ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.349]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"17.3725ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.350]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"17.9006ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"28.4026ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.380]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"29.0897ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.407]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895cc26f2a861725753","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"14.9074ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.511]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"104.4653ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.512]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"105.0541ms","duration_ms":105}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.515]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"107.9752ms","duration_ms":107}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.515]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"107.9752ms","duration_ms":107}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.515]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"108.5678ms","duration_ms":108}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.526]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"14.4175ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.551]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"12.2484ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.556]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"17.2798ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.583]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"26.9782ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.617]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"23.9624ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.652]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"24.5531ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.680]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18606895d417de64f154e5e8","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"27.7673ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.263]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"66.0125ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.327]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"26.3355ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.375]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"48.3209ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.408]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"107.537ms","duration_ms":107}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.409]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"108.5868ms","duration_ms":108}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.409]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"33.4154ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.446]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"36.6706ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.446]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"37.1761ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.447]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0b9fcf466599b8e","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"37.7116ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.480]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0c1dfb446f812a3","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"33.2274ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.529]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0c1dfb446f812a3","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"107.2417ms","duration_ms":107}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.531]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0c1dfb446f812a3","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"50.778ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.555]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0c1dfb446f812a3","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"26.0154ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.605]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689ba0c1dfb446f812a3","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"22.3201ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.630]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"25.0578ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.630]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"25.0578ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.630]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"25.0578ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.630]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"25.5683ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.649]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"18.6579ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.680]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"75.2459ms","duration_ms":75}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.716]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"22.6651ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.736]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"42.6966ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.761]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"24.7932ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.788]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"17.8282ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"15.4167ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.842]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"23.6247ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.209]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689bb1907120a9268633","sql":"SELECT * FROM `business_payment_transactions` WHERE `transaction_no` = ? and (deleted_at IS NULL) LIMIT 1, [*********************]","duration":"93.7699ms","duration_ms":93}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.274]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"31.4755ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.275]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? LIMIT 1, [%******************%]","duration":"36.3549ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.275]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"34.0501ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5a05f08c906a54c","sql":"SELECT id, status, amount, created_at as initiated_at, completed_at, channel_transaction_no,error_message\n\t\t FROM `business_payment_transactions` WHERE `order_id` = ? and `user_id` = ? and (deleted_at IS NULL) and `type` = ? ORDER BY created_at ASC, [89 7 DISBURSEMENT]","duration":"52.3847ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5a05f08c906a54c","sql":"SELECT brb.id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbrb.total_refund_amount,\n\t\t\tbrb.status,\n\t\t\tbrb.due_date,\n\t\t\tbrb.total_waive_amount,\n\t\t\tbrb.paid_at as payment_time FROM business_repayment_bills brb WHERE brb.order_id = ? and brb.user_id = ? ORDER BY brb.period_number ASC, [89 7]","duration":"52.3847ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5a05f08c906a54c","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [19]","duration":"52.991ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.330]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) and blo.order_no like ? ORDER BY blo.id DESC LIMIT 1, [%******************%]","duration":"55.505ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.400]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT count(*) as count FROM business_payment_transactions bpt WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) LIMIT 1, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"69.2071ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.409]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (7)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","duration":"14.9696ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.428]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"19.0278ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.428]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT bpt.id,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.amount,\n\t\t\tbpt.status,\n\t\t\tbpt.type,\n\t\t\tbpt.withhold_type,\n\t\t\tbpt.created_at as initiated_at,\n\t\t\tbpt.completed_at,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.error_message,\n\t\t\tbpt.offline_payment_voucher,\n\t\t\tbrb.period_number,\n\t\t\tbrb.id as bill_id,\n\t\t\tbpc.channel_name as payment_type,\n\t\t\t(select sum(amount) from business_payment_transactions where related_transaction_no = bpt.transaction_no and type = 'REFUND' and status in (1, 2)) as refund_amount FROM business_payment_transactions bpt LEFT JOIN business_repayment_bills brb ON bpt.bill_id = brb.id  LEFT JOIN business_payment_channels bpc ON bpt.payment_channel_id = bpc.id WHERE bpt.order_id = ? and (bpt.deleted_at IS NULL) and bpt.type IN (?,?,?,?,?) ORDER BY bpt.created_at DESC LIMIT 10, [89 REPAYMENT REFUND WITHHOLD PARTIAL_OFFLINE_REPAYMENT MANUAL_WITHHOLD]","duration":"28.5679ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.449]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"20.1121ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.478]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689db5be7c0411c6d4bf","sql":"\n\t\tSELECT\n\t\t\tid, order_no, user_id, product_rule_id, loan_amount, principal,\n\t\t\ttotal_interest, total_guarantee_fee, total_other_fees, total_repayable_amount,\n\t\t\tamount_paid, channel_id, payment_channel_id, customer_origin, status,\n\t\t\tis_freeze, review_status, review_remark,\n\t\t\tcreated_at, disbursed_at, completed_at\n\t\tFROM business_loan_orders\n\t\tWHERE id = ?\n\t, [89]","duration":"17.4527ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.500]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"\n\t\tSELECT riskScore\n\t\tFROM business_app_account\n\t\tWHERE id = ? AND riskScore > 0\n\t, [7]","duration":"22.0275ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.500]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"\n\t\tSELECT\n\t\t\tid, name, mobile, idCard, idCardFrontUrl, idCardBackUrl,facePhotoUrl,\n\t\t\trepeatBuyNum, riskScore, address, province, city,\n\t\t\tlastLoginIp, lastLoginLocation, createtime, status,\n\t\t\tallQuota, reminderQuota,\n\t\t\temergencyContact0Name, emergencyContact0Phone, emergencyContact0Relation,\n\t\t\temergencyContact1Name, emergencyContact1Phone, emergencyContact1Relation\n\t\tFROM business_app_account\n\t\tWHERE id = ?\n\t, [7]","duration":"22.61ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.500]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"\n\t\tSELECT\n\t\t\tr.id,\n\t\t\tr.order_id,\n\t\t\tr.content,\n\t\t\tr.user_id,\n\t\t\tCOALESCE(u.username, '') as user_name,\n\t\t\tr.create_time\n\t\tFROM business_order_remarks r\n\t\tLEFT JOIN business_account u ON r.user_id = u.id\n\t\tWHERE r.order_id = ?\n\t\tORDER BY r.create_time DESC\n\t, [89]","duration":"22.61ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.500]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"\n\t\tSELECT id, channel_name, channel_code\n\t\tFROM business_payment_channels\n\t\tWHERE id = ?\n\t, [1]","duration":"22.61ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.500]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"\n\t\tSELECT\n\t\t\tCOUNT(*) as total_orders,\n\t\t\tSUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as in_progress_orders,\n\t\t\tSUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_orders,\n\t\t\tMIN(created_at) as first_order_time\n\t\tFROM business_loan_orders\n\t\tWHERE user_id = ?\n\t, [7]","duration":"22.61ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.537]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"\n\t\tSELECT risk_score\n\t\tFROM risk_evaluations\n\t\tWHERE customer_id = ?\n\t\tORDER BY evaluation_time DESC\n\t\tLIMIT 1\n\t, [7]","duration":"36.221ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [7]","duration":"25.4525ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.573]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"SELECT baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,\n\t\t\tbaa.degree, baa.marry, baa.occupation, baa.yearRevenue,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount FROM business_app_account baa LEFT JOIN channel c ON baa.channelId = c.id  LEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1 WHERE baa.id = ? and baa.deletedAt = ? LIMIT 1, [7 0]","duration":"25.4525ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.591]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"SELECT * FROM `ocr_identity_records` WHERE `user_id` = ? ORDER BY created_at DESC LIMIT 1, [7]","duration":"17.5652ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.617]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"SELECT * FROM `business_bank_cards` WHERE `user_id` = ? and `card_status` = ? ORDER BY id, [7 1]","duration":"18.4251ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.644]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"SELECT count(*) as count FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) LIMIT 1, []","duration":"14.1673ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1860689dc2be7eb8aaa2ef6a","sql":"SELECT \n\tba.username as recorder,\n\tcr.result as result,\n\tcr.note as note,\n\tcr.bill_id as bill_id,\n\tDATE_FORMAT(cr.created_at, '%Y-%m-%d %H:%i:%s') as record_time\n\t FROM collection_record cr LEFT JOIN business_account ba ON cr.admin_id = ba.id WHERE (cr.bill_id in (select id from business_repayment_bills where order_id = 89)) ORDER BY cr.id DESC LIMIT 20, []","duration":"45.3666ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-08-30 09:55:00.041]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"40.1296ms","duration_ms":40}
