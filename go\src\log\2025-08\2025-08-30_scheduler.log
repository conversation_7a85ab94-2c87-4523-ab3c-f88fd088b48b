{"level":"dev.info","ts":"[2025-08-30 09:38:43.235]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.239]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.239]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.239]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.241]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.242]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.243]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.243]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.243]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.244]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.244]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.244]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.244]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.245]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.245]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.245]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.245]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.245]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-30 09:38:43.245]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0412504,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.041]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.167]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.1676936,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:39:00.167]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.301]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.302]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.302]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.302]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.304]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.305]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.305]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.305]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.305]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.305]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.305]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.306]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.306]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.306]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.306]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-30 09:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:41:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0772948,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:41:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.258]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.260]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.260]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.260]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.262]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.263]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.264]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.264]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.264]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-30 09:51:09.264]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.022]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0216235,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.022]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.028]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":1,"duration":0.027819,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.028]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.029]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":3,"duration":0.0286468,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:52:00.029]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.754]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.755]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.755]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.755]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.756]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.757]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-30 09:52:31.758]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-30 09:53:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:53:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0243776,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:53:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.069]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0684283,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.069]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0704857,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.078]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0773365,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.078]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.092]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":3,"duration":0.0913403,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:54:00.092]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.787]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.787]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.788]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.788]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.789]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.790]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.791]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.791]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.792]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.792]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.792]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.792]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.793]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.793]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.793]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.793]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.793]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-08-30 09:54:28.793]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-08-30 09:55:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-08-30 09:55:00.041]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0407173,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-08-30 09:55:00.041]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
