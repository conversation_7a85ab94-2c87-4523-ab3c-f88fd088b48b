package lock

import (
	"fmt"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// TestRedisLockPerformanceAnalysis 详细分析Redis锁性能瓶颈
func TestRedisLockPerformanceAnalysis(t *testing.T) {
	manager := NewRedisLockManager()

	// 测试1: 单线程性能基准
	t.Log("=== 测试1: 单线程性能基准 ===")
	singleThreadOps := testSingleThreadPerformance(t, manager)

	// 测试2: 多线程性能对比
	t.Log("=== 测试2: 多线程性能对比 ===")
	multiThreadOps := testMultiThreadPerformance(t, manager)

	// 测试3: 网络延迟分析
	t.Log("=== 测试3: 网络延迟分析 ===")
	testNetworkLatency(t, manager)

	// 测试4: 锁竞争影响
	t.Log("=== 测试4: 锁竞争影响 ===")
	testLockContention(t, manager)

	// 性能总结
	t.Logf("=== 性能总结 ===")
	t.Logf("单线程吞吐量: %.0f ops/sec", singleThreadOps)
	t.Logf("多线程吞吐量: %.0f ops/sec", multiThreadOps)
	t.Logf("性能下降: %.1f%%", (singleThreadOps-multiThreadOps)/singleThreadOps*100)
}

// testSingleThreadPerformance 测试单线程性能
func testSingleThreadPerformance(t *testing.T, manager LockManager) float64 {
	const numOperations = 1000
	
	startTime := time.Now()
	
	for i := 0; i < numOperations; i++ {
		key := fmt.Sprintf("single:perf:%d", i)
		lock := manager.GetLock(key, 10*time.Second)
		
		success, _ := lock.TryLock()
		if success {
			lock.Unlock()
		}
	}
	
	duration := time.Since(startTime)
	opsPerSecond := float64(numOperations) / duration.Seconds()
	
	t.Logf("单线程测试: %d 操作, 耗时: %v, 吞吐量: %.0f ops/sec", 
		numOperations, duration, opsPerSecond)
	
	return opsPerSecond
}

// testMultiThreadPerformance 测试多线程性能
func testMultiThreadPerformance(t *testing.T, manager LockManager) float64 {
	const numOperations = 1000
	const numGoroutines = 10
	
	var wg sync.WaitGroup
	var totalOps int64
	
	startTime := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			opsPerGoroutine := numOperations / numGoroutines
			for j := 0; j < opsPerGoroutine; j++ {
				key := fmt.Sprintf("multi:perf:%d:%d", id, j)
				lock := manager.GetLock(key, 10*time.Second)
				
				success, _ := lock.TryLock()
				if success {
					lock.Unlock()
				}
				
				atomic.AddInt64(&totalOps, 1)
			}
		}(i)
	}
	
	wg.Wait()
	
	duration := time.Since(startTime)
	opsPerSecond := float64(totalOps) / duration.Seconds()
	
	t.Logf("多线程测试: %d 操作, %d goroutines, 耗时: %v, 吞吐量: %.0f ops/sec", 
		totalOps, numGoroutines, duration, opsPerSecond)
	
	return opsPerSecond
}

// testNetworkLatency 测试网络延迟
func testNetworkLatency(t *testing.T, manager LockManager) {
	const numSamples = 100
	
	var totalLatency time.Duration
	var maxLatency time.Duration
	var minLatency time.Duration = time.Hour
	
	for i := 0; i < numSamples; i++ {
		key := fmt.Sprintf("latency:test:%d", i)
		lock := manager.GetLock(key, 10*time.Second)
		
		start := time.Now()
		success, _ := lock.TryLock()
		latency := time.Since(start)
		
		if success {
			unlockStart := time.Now()
			lock.Unlock()
			unlockLatency := time.Since(unlockStart)
			
			// 计算总延迟（加锁+解锁）
			totalOpLatency := latency + unlockLatency
			totalLatency += totalOpLatency
			
			if totalOpLatency > maxLatency {
				maxLatency = totalOpLatency
			}
			if totalOpLatency < minLatency {
				minLatency = totalOpLatency
			}
		}
	}
	
	avgLatency := totalLatency / numSamples
	
	t.Logf("网络延迟分析:")
	t.Logf("- 平均延迟: %v", avgLatency)
	t.Logf("- 最大延迟: %v", maxLatency)
	t.Logf("- 最小延迟: %v", minLatency)
	t.Logf("- 理论最大吞吐量: %.0f ops/sec", 1.0/avgLatency.Seconds())
}

// testLockContention 测试锁竞争对性能的影响
func testLockContention(t *testing.T, manager LockManager) {
	const numGoroutines = 50
	const opsPerGoroutine = 20
	
	// 测试1: 无竞争（每个goroutine使用不同的key）
	t.Log("测试无竞争场景:")
	noContentionOps := testNoContention(t, manager, numGoroutines, opsPerGoroutine)
	
	// 测试2: 高竞争（所有goroutine使用相同的key）
	t.Log("测试高竞争场景:")
	highContentionOps := testHighContention(t, manager, numGoroutines, opsPerGoroutine)
	
	t.Logf("竞争影响分析:")
	t.Logf("- 无竞争吞吐量: %.0f ops/sec", noContentionOps)
	t.Logf("- 高竞争吞吐量: %.0f ops/sec", highContentionOps)
	t.Logf("- 竞争导致的性能下降: %.1f%%", (noContentionOps-highContentionOps)/noContentionOps*100)
}

func testNoContention(t *testing.T, manager LockManager, numGoroutines, opsPerGoroutine int) float64 {
	var wg sync.WaitGroup
	var totalOps int64
	
	startTime := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			for j := 0; j < opsPerGoroutine; j++ {
				key := fmt.Sprintf("no_contention:%d:%d", id, j) // 每个操作使用不同key
				lock := manager.GetLock(key, 5*time.Second)
				
				success, _ := lock.TryLock()
				if success {
					lock.Unlock()
				}
				
				atomic.AddInt64(&totalOps, 1)
			}
		}(i)
	}
	
	wg.Wait()
	
	duration := time.Since(startTime)
	opsPerSecond := float64(totalOps) / duration.Seconds()
	
	t.Logf("  无竞争: %d 操作, 耗时: %v, 吞吐量: %.0f ops/sec", 
		totalOps, duration, opsPerSecond)
	
	return opsPerSecond
}

func testHighContention(t *testing.T, manager LockManager, numGoroutines, opsPerGoroutine int) float64 {
	var wg sync.WaitGroup
	var totalOps int64
	var successOps int64
	
	startTime := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(goroutineID int) {
			defer wg.Done()

			for j := 0; j < opsPerGoroutine; j++ {
				key := "high_contention_key" // 所有操作使用相同key
				lock := manager.GetLock(key, 5*time.Second)

				success, _ := lock.TryLock()
				if success {
					atomic.AddInt64(&successOps, 1)
					// 模拟一些工作
					time.Sleep(1 * time.Millisecond)
					lock.Unlock()
				}

				atomic.AddInt64(&totalOps, 1)
			}
		}(i)
	}
	
	wg.Wait()
	
	duration := time.Since(startTime)
	opsPerSecond := float64(totalOps) / duration.Seconds()
	successRate := float64(successOps) / float64(totalOps) * 100
	
	t.Logf("  高竞争: %d 操作, 成功: %d (%.1f%%), 耗时: %v, 吞吐量: %.0f ops/sec", 
		totalOps, successOps, successRate, duration, opsPerSecond)
	
	return opsPerSecond
}
