package lock

import (
	"fmt"
	"testing"
	"time"
)

// TestQuickRedisPerformance 快速Redis性能测试
func TestQuickRedisPerformance(t *testing.T) {
	manager := NewRedisLockManager()

	// 测试单个操作的耗时
	t.Log("=== 单个操作耗时分析 ===")
	
	for i := 0; i < 10; i++ {
		key := fmt.Sprintf("quick:test:%d", i)
		lock := manager.GetLock(key, 10*time.Second)
		
		// 测试TryLock耗时
		start := time.Now()
		success, _ := lock.TryLock()
		lockDuration := time.Since(start)
		
		if success {
			// 测试Unlock耗时
			unlockStart := time.Now()
			lock.Unlock()
			unlockDuration := time.Since(unlockStart)
			
			totalDuration := lockDuration + unlockDuration
			t.Logf("操作 %d: TryLock=%v, Unlock=%v, 总计=%v", 
				i, lockDuration, unlockDuration, totalDuration)
		} else {
			t.Logf("操作 %d: TryLock失败, 耗时=%v", i, lockDuration)
		}
		
		// 如果单个操作超过100ms，说明有严重问题
		if lockDuration > 100*time.Millisecond {
			t.<PERSON><PERSON><PERSON>("单个TryLock操作耗时过长: %v", lockDuration)
			break
		}
	}
}

// TestRedisConnectionLatency 测试Redis连接延迟
func TestRedisConnectionLatency(t *testing.T) {
	t.Log("=== Redis连接延迟测试 ===")
	
	// 直接测试Redis连接
	manager := NewRedisLockManager()
	
	// 测试多次连续操作
	var totalLatency time.Duration
	const numTests = 5
	
	for i := 0; i < numTests; i++ {
		key := fmt.Sprintf("latency:test:%d", i)
		lock := manager.GetLock(key, 5*time.Second)
		
		start := time.Now()
		success, _ := lock.TryLock()
		latency := time.Since(start)
		totalLatency += latency
		
		t.Logf("连接测试 %d: 延迟=%v, 成功=%v", i, latency, success)
		
		if success {
			lock.Unlock()
		}
		
		// 短暂休息避免过于频繁
		time.Sleep(100 * time.Millisecond)
	}
	
	avgLatency := totalLatency / numTests
	t.Logf("平均延迟: %v", avgLatency)
	
	if avgLatency > 50*time.Millisecond {
		t.Logf("警告: Redis连接延迟较高 (%v)", avgLatency)
	}
}

// TestMemoryLockComparison 对比内存锁性能
func TestMemoryLockComparison(t *testing.T) {
	t.Log("=== 内存锁 vs Redis锁性能对比 ===")
	
	// 测试内存锁
	memoryManager := NewSimpleLockManager()
	t.Log("测试内存锁性能:")
	memoryOps := testManagerPerformance(t, memoryManager, "memory", 100)
	
	// 测试Redis锁
	redisManager := NewRedisLockManager()
	t.Log("测试Redis锁性能:")
	redisOps := testManagerPerformance(t, redisManager, "redis", 10) // 减少操作数
	
	t.Logf("性能对比:")
	t.Logf("- 内存锁: %.0f ops/sec", memoryOps)
	t.Logf("- Redis锁: %.0f ops/sec", redisOps)
	if memoryOps > 0 && redisOps > 0 {
		ratio := memoryOps / redisOps
		t.Logf("- 内存锁比Redis锁快 %.1f 倍", ratio)
	}
}

func testManagerPerformance(t *testing.T, manager LockManager, name string, numOps int) float64 {
	start := time.Now()
	successCount := 0
	
	for i := 0; i < numOps; i++ {
		key := fmt.Sprintf("%s:perf:%d", name, i)
		lock := manager.GetLock(key, 5*time.Second)
		
		success, _ := lock.TryLock()
		if success {
			successCount++
			lock.Unlock()
		}
		
		// 如果单个操作超过1秒，提前退出
		if time.Since(start) > time.Duration(i+1)*time.Second {
			t.Logf("%s锁: 操作 %d 超时，提前退出", name, i)
			break
		}
	}
	
	duration := time.Since(start)
	opsPerSecond := float64(successCount) / duration.Seconds()
	
	t.Logf("%s锁: %d/%d 成功, 耗时: %v, 吞吐量: %.0f ops/sec", 
		name, successCount, numOps, duration, opsPerSecond)
	
	return opsPerSecond
}
