package lock

import (
	"fmt"
	"testing"
	"time"
)

// TestSimpleLockDebug 简化的锁测试，用于调试
func TestSimpleLockDebug(t *testing.T) {
	manager := NewSimpleLockManager()

	// 测试单个锁
	t.Log("开始测试单个锁...")
	key := "debug:test:1"
	lock := manager.GetLock(key, 100*time.Millisecond)
	
	t.Logf("锁创建完成，key: %s", key)
	
	// 记录开始时间
	start := time.Now()
	
	// 尝试加锁
	t.Log("开始加锁...")
	result, err := lock.Lock()
	duration := time.Since(start)
	
	if err != nil {
		t.<PERSON><PERSON>("加锁失败: %v, 耗时: %v", err, duration)
		return
	}
	
	t.Logf("加锁成功，耗时: %v", duration)
	
	// 检查锁状态
	status := lock.GetLockStatus()
	t.Logf("锁状态: %+v", status)
	
	// 解锁
	t.Log("开始解锁...")
	result.Unlock()
	t.Log("解锁完成")
}

// TestMultipleLocks 测试多个不同的锁
func TestMultipleLocks(t *testing.T) {
	manager := NewSimpleLockManager()

	const numLocks = 10 // 减少到10个锁进行测试
	
	t.Logf("开始测试 %d 个锁...", numLocks)
	
	for i := 0; i < numLocks; i++ {
		key := fmt.Sprintf("debug:test:%d", i)
		lock := manager.GetLock(key, 100*time.Millisecond)
		
		t.Logf("测试锁 %d, key: %s", i, key)
		
		start := time.Now()
		result, err := lock.Lock()
		duration := time.Since(start)
		
		if err != nil {
			t.Errorf("锁 %d 加锁失败: %v, 耗时: %v", i, err, duration)
			return
		}
		
		if duration > 200*time.Millisecond {
			t.Errorf("锁 %d 加锁耗时过长: %v", i, duration)
		}
		
		t.Logf("锁 %d 加锁成功，耗时: %v", i, duration)
		
		// 立即解锁
		result.Unlock()
		t.Logf("锁 %d 解锁完成", i)
	}
	
	t.Log("所有锁测试完成")
}

// TestLockTimeout 测试锁超时机制
func TestLockTimeout(t *testing.T) {
	manager := NewSimpleLockManager()
	
	key := "debug:timeout:test"
	
	// 第一个锁，不设置超时（使用默认30秒）
	lock1 := manager.GetLock(key, 100*time.Millisecond)
	
	t.Log("第一个锁开始加锁...")
	result1, err := lock1.Lock()
	if err != nil {
		t.Errorf("第一个锁加锁失败: %v", err)
		return
	}
	t.Log("第一个锁加锁成功")
	
	// 第二个锁，设置短超时
	lock2 := manager.GetLock(key, 100*time.Millisecond).WithTimeout(500 * time.Millisecond)
	
	t.Log("第二个锁开始加锁（应该超时）...")
	start := time.Now()
	_, err = lock2.Lock()
	duration := time.Since(start)
	
	if err == nil {
		t.Error("第二个锁应该超时失败，但成功了")
	} else {
		t.Logf("第二个锁正确超时: %v, 耗时: %v", err, duration)
	}
	
	// 释放第一个锁
	result1.Unlock()
	t.Log("第一个锁解锁完成")
}
