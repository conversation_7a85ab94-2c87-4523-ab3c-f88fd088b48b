package lock

import (
	"context"
	"os"
	"sync"
	"testing"
	"time"

	"fincore/global"
	"fincore/utils/config"
	"fincore/utils/utilstool/goredis"
)

// 测试初始化
func init() {
	// 设置环境变量
	os.Setenv("ENV", "dev")

	// 初始化配置
	global.App = &global.Application{
		Config: config.InitializeConfig("fincore"),
	}

	// 初始化Redis客户端
	goredis.InitRedisClient()
}

// TestRedisLockBasic 测试Redis锁基本功能
func TestRedisLockBasic(t *testing.T) {
	// 跳过测试如果Redis不可用
	if !isRedisAvailable() {
		t.<PERSON><PERSON>("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 测试基本加锁解锁
	lock := manager.GetLock("test:redis:basic", 30*time.Second)

	// 加锁
	lock.Lock()

	// 验证锁状态
	stats := manager.GetStats()
	if len(stats) == 0 {
		t.Error("应该有一个锁的统计信息")
	}

	// 解锁
	lock.Unlock()
}

// TestRedisLockTryLock 测试Redis锁TryLock功能
func TestRedisLockTryLock(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 清理可能存在的锁
	testKey := "test:redis:trylock"
	manager.UnlockByKey(testKey)
	time.Sleep(10 * time.Millisecond)

	// 第一个锁成功获取
	lock1 := manager.GetLock(testKey, 30*time.Second)
	success1, _ := lock1.TryLock()
	if !success1 {
		t.Error("第一次TryLock应该成功")
	}

	// 第二个锁应该失败
	lock2 := manager.GetLock(testKey, 30*time.Second)
	success2, _ := lock2.TryLock()
	if success2 {
		t.Error("第二次TryLock应该失败")
	}

	// 释放第一个锁
	lock1.Unlock()

	// 等待一小段时间确保Redis操作完成
	time.Sleep(100 * time.Millisecond)

	// 检查Redis中是否还有锁
	client := goredis.GetRedisClient()
	exists, _ := client.Exists(context.Background(), "lock:"+testKey).Result()
	t.Logf("释放锁后Redis中锁是否存在: %d", exists)

	// 现在第二个锁应该能成功
	success3, _ := lock2.TryLock()
	if !success3 {
		t.Error("释放锁后TryLock应该成功")
	}

	lock2.Unlock()
}

// TestRedisLockConcurrency 测试Redis锁并发安全性
func TestRedisLockConcurrency(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()
	key := "test:redis:concurrency"

	var wg sync.WaitGroup
	var successCount int
	var mu sync.Mutex

	// 启动多个goroutine尝试获取同一个锁
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			lock := manager.GetLock(key, 5*time.Second)
			success, _ := lock.TryLock()
			if success {
				mu.Lock()
				successCount++
				mu.Unlock()

				// 持有锁一段时间
				time.Sleep(100 * time.Millisecond)
				lock.Unlock()
			}
		}(i)
	}

	wg.Wait()

	// 只有一个goroutine应该成功获取锁
	if successCount != 1 {
		t.Errorf("期望只有1个goroutine成功获取锁，实际: %d", successCount)
	}
}

// TestRedisLockExpiration 测试Redis锁过期功能
func TestRedisLockExpiration(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 创建一个短期锁
	lock := manager.GetLock("test:redis:expiration", 1*time.Second)
	lock.Lock()

	// 等待锁过期
	time.Sleep(2 * time.Second)

	// 现在应该能获取到锁
	lock2 := manager.GetLock("test:redis:expiration", 30*time.Second)
	success, _ := lock2.TryLock()
	if !success {
		t.Error("锁过期后应该能重新获取")
	}

	lock2.Unlock()
}

// TestRedisLockRenewal 测试Redis锁自动续期功能
func TestRedisLockRenewal(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 创建一个短期锁，但会自动续期
	lock := manager.GetLock("test:redis:renewal", 2*time.Second)
	lock.Lock()

	// 等待超过原始过期时间
	time.Sleep(3 * time.Second)

	// 锁应该仍然被持有（因为自动续期）
	lock2 := manager.GetLock("test:redis:renewal", 30*time.Second)
	success, _ := lock2.TryLock()
	if success {
		t.Error("锁应该仍然被持有（自动续期）")
		lock2.Unlock()
	}

	// 释放原锁
	lock.Unlock()

	// 等待一小段时间确保Redis操作完成
	time.Sleep(10 * time.Millisecond)

	// 现在应该能获取到锁
	success2, _ := lock2.TryLock()
	if !success2 {
		t.Error("释放锁后应该能重新获取")
	}

	lock2.Unlock()
}

// TestRedisLockWithLogger 测试带日志的Redis锁
func TestRedisLockWithLogger(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	// 跳过日志测试，因为需要完整的日志系统初始化
	t.Skip("跳过日志测试，需要完整的应用环境")
}

// TestRedisLockChainedOperations 测试Redis锁链式操作
func TestRedisLockChainedOperations(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 测试链式操作
	lock, err := manager.GetLock("test:redis:chained", 30*time.Second).
		WithTimeout(5 * time.Second).
		Lock()

	if err != nil {
		t.Fatal(err)
	}
	key := lock.GetKey()

	if key != "lock:test:redis:chained" {
		t.Errorf("期望key为 'lock:test:redis:chained'，实际: %s", key)
	}
}

// TestRedisLockWithContext 测试带上下文的Redis锁
func TestRedisLockWithContext(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	lock := manager.GetLock("test:redis:context", 30*time.Second).
		WithContext(ctx)

	lock.Lock()

	// 取消上下文应该停止看门狗
	cancel()

	// 等待一段时间让看门狗停止
	time.Sleep(100 * time.Millisecond)

	lock.Unlock()
}

// TestRedisLockStats 测试Redis锁统计信息
func TestRedisLockStats(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 创建几个锁
	lock1 := manager.GetLock("test:redis:stats:1", 30*time.Second)
	lock2 := manager.GetLock("test:redis:stats:2", 30*time.Second)

	lock1.Lock()
	lock2.Lock()

	// 获取统计信息
	stats := manager.GetStats()
	if len(stats) < 2 {
		t.Errorf("期望至少2个锁的统计信息，实际: %d", len(stats))
	}

	// 检查Redis特有的统计信息
	if redisManager, ok := manager.(*RedisLockManager); ok {
		redisStats := redisManager.GetRedisLockStats()
		if len(redisStats) < 2 {
			t.Errorf("期望至少2个Redis锁的统计信息，实际: %d", len(redisStats))
		}
	}

	lock1.Unlock()
	lock2.Unlock()
}

// TestRedisLockCleanup 测试Redis锁清理功能
func TestRedisLockCleanup(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁测试")
	}

	manager := NewRedisLockManager()

	// 创建一些短期锁
	for i := 0; i < 5; i++ {
		lock := manager.GetLock("test:redis:cleanup:"+string(rune(i)), 1*time.Second)
		lock.Lock()
		// 不解锁，让它们过期
	}

	// 等待锁过期
	time.Sleep(2 * time.Second)

	// 清理过期锁
	cleaned := manager.CleanExpiredLocks()
	t.Logf("清理了 %d 个过期锁", cleaned)
}

// TestLockFactory 测试锁工厂功能
func TestLockFactory(t *testing.T) {
	factory := NewLockFactory()

	// 测试当前配置（根据配置文件决定）
	currentType := factory.GetCurrentLockType()
	t.Logf("当前锁类型: %s", currentType)

	// 配置文件中设置为redis，所以这里应该是redis
	if currentType != LockTypeRedis && currentType != LockTypeMemory {
		t.Errorf("锁类型应该是redis或memory，实际: %s", currentType)
	}

	// 测试获取锁
	lock := factory.GetLock("test:factory", 30*time.Second)
	if lock == nil {
		t.Error("应该能获取到锁")
	}

	// 测试健康状态
	status := factory.GetHealthStatus()
	configuredType := status["configured_type"].(string)
	t.Logf("配置类型: %s", configuredType)

	// 验证配置类型是有效的
	if configuredType != string(LockTypeMemory) && configuredType != string(LockTypeRedis) {
		t.Errorf("配置类型应该是memory或redis，实际: %s", configuredType)
	}
}

// TestLockFactoryRedisConfig 测试Redis配置的锁工厂
func TestLockFactoryRedisConfig(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis工厂测试")
	}

	// 创建Redis配置
	config := &LockConfig{
		Type: LockTypeRedis,
		Redis: RedisConfig{
			DefaultExpiration: 30,
			RenewalInterval:   10,
			MaxRetryTimes:     3,
			RetryIntervalMs:   100,
			EnableWatchdog:    true,
		},
	}

	factory := NewLockFactory()
	err := factory.UpdateConfig(config)
	if err != nil {
		t.Errorf("更新配置失败: %v", err)
	}

	// 检查是否使用Redis锁
	if factory.IsUsingRedis() {
		t.Log("成功切换到Redis锁")
	} else {
		t.Log("Redis不可用，降级到内存锁")
	}
}

// TestGlobalLockFunctions 测试全局锁函数
func TestGlobalLockFunctions(t *testing.T) {
	// 测试全局函数
	lock := GetLock("test:global", 30*time.Second)
	lock.Lock()
	defer lock.Unlock()

	// 测试锁类型
	lockType := GetCurrentLockType()
	if lockType != LockTypeMemory && lockType != LockTypeRedis {
		t.Errorf("未知的锁类型: %s", lockType)
	}

	// 测试健康状态
	status := GetLockHealthStatus()
	if status == nil {
		t.Error("应该能获取健康状态")
	}

	// 测试统计信息
	stats := GetStats()
	if len(stats) == 0 {
		t.Error("应该有锁的统计信息")
	}
}

// isRedisAvailable 检查Redis是否可用
func isRedisAvailable() bool {
	defer func() {
		if r := recover(); r != nil {
			// Redis连接出现panic，认为不可用
		}
	}()

	manager := NewRedisLockManager()
	if redisManager, ok := manager.(*RedisLockManager); ok {
		return redisManager.IsHealthy()
	}

	return false
}
