<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue';
  import {
    cancelOfflinePayment,
    getOrderBillInfo,
    getOrderPaymentRecords,
    getPaymentStatus,
    processPartialOfflinePayment,
    processRefund, refreshRefundStatus,
    updateBillDueDate,
    waiveBillAmount
  } from '@/api/ordermanagement';
  import { useRoute } from 'vue-router';
  const $route = useRoute();
  import type { FormInstance } from 'element-plus';
  import { Message } from '@arco-design/web-vue';

  const props = defineProps({
    orderDetail: {
      type: Object,
      required: true,
    },
  });

  // 检查当前用户是否有权限操作订单
  const hasOrderPermission = (): boolean => {
    console.log('------权限检查开始------');
    if (!props.orderDetail) {
      console.log('订单详情不存在');
      return false;
    }

    // 获取当前用户ID
    const userInfoStr = localStorage.getItem('userInfo');
    if (!userInfoStr) {
      console.log('userInfo不存在');
      return false;
    }

    try {
      const userInfo = JSON.parse(userInfoStr);
      console.log('userInfo:', userInfo);

      // 获取当前用户ID
      const currentUserId = Number(userInfo.uid || 0);
      console.log('当前用户ID:', currentUserId, '类型:', typeof currentUserId);

      // 获取订单分配的业务员ID
      const salesAssigneeId = Number(props.orderDetail.sales_assignee_id || 0);
      console.log(
        '订单业务员ID:',
        salesAssigneeId,
        '类型:',
        typeof salesAssigneeId
      );

      // 判断用户是否有权限操作 - 必须是订单的分配业务员
      const hasPermission =
        currentUserId > 0 && currentUserId === salesAssigneeId;
      console.log('权限判断结果:', hasPermission);

      console.log('------权限检查结束------');
      return hasPermission;
    } catch (e) {
      console.error('解析userInfo失败:', e);
      return false;
    }
  };

  //  账单列表
  const bill_list = ref();
  const disbursement_records = ref([]); // 打款记录
  const total_init = reactive({
    total_repayable_amount: 0,
    pre_principal: 0,
    pre_interest: 0,
    pre_guarantee_fee: 0,
  })
  function getBillList() {
    getOrderBillInfo(String($route.query.orderNo)).then((res) => {
      bill_list.value = res.bill_list;

      total_init.total_repayable_amount = res.total_repayable_amount;
      total_init.pre_principal = res.pre_principal;
      total_init.pre_interest = res.pre_interest;
      total_init.pre_guarantee_fee = res.pre_guarantee_fee;

      disbursement_records.value = res.disbursement_records || [];
    });
  }
  // 订单支付记录
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const recordsList = ref([]);
  function getOrderPayRecordsList() {
    getOrderPaymentRecords({
      page: pagination.page,
      pageSize: pagination.pageSize,
      order_no: String($route.query.orderNo),
    }).then((res) => {
      pagination.total = res.total;
      res.data.forEach((item: any) => {
        item.loading = false;
        item.refundLoading = false;
      })
      recordsList.value = res.data || [];
    });
  }
  const handleSizeChange = (val: number) => {
    console.log(`${val} items per page`);
    pagination.pageSize = val;
    pagination.page = 1;
    getOrderPayRecordsList();
  };
  const handleCurrentChange = (val: number) => {
    console.log(`current page: ${val}`);
    pagination.page = val;
    getOrderPayRecordsList();
  };
  onMounted(() => {
    getReastData(false);
  });

  // 账单时间
  let dialogFormTimeVisible = ref(false);
  const formTimeRef = ref<FormInstance>();
  const formTime = reactive({
    bill_id: 0,
    due_date: '',
  });
  function handleShowFormTime(id: number) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    formTime.bill_id = id;
    formTime.due_date = '';
    dialogFormTimeVisible.value = true;
  }
  function handleHideFormTime() {
    formTime.bill_id = 0;
    formTime.due_date = '';
    dialogFormTimeVisible.value = false;
  }
  function handleFromTimeConfirm(formTimeEf: FormInstance) {
    formTimeEf.validate((valid) => {
      if (valid) {
        console.log(formTime);
        console.log('submit!');
        updateBillDueDate(formTime.bill_id, formTime.due_date).then((res) => {
          Message.success('修改成功');
          getReastData();
          handleHideFormTime();
        });
      } else {
        console.log('error submit!');
      }
    });
  }

  // 部分支付
  let dialogFormPartialPayVisible = ref(false);
  const formPartialPayRef = ref<FormInstance>();
  const formFormPartial = reactive({
    bill_id: 0,
    amount: '',
    payment_channel: '',
    voucher: '1',
  });
  function handleShowFormPartial(id: number) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    formFormPartial.bill_id = id;
    formFormPartial.amount = '';
    formFormPartial.payment_channel = '';
    // formFormPartial.voucher = ''
    dialogFormPartialPayVisible.value = true;
  }
  function handleHideFormPartial() {
    formFormPartial.bill_id = 0;
    formFormPartial.amount = '';
    formFormPartial.payment_channel = '';
    // formFormPartial.voucher = ''
    dialogFormPartialPayVisible.value = false;
  }
  function handleFormPartialConfirm(formPartialPayEl: FormInstance) {
    formPartialPayEl.validate((valid) => {
      if (valid) {
        console.log(formFormPartial);
        console.log('submit!');
        processPartialOfflinePayment({
          bill_id: formFormPartial.bill_id,
          payment_channel: formFormPartial.payment_channel,
          voucher: formFormPartial.voucher,
          amount: Number(formFormPartial.amount),
        }).then((res) => {
          Message.success('操作成功');
          getReastData();
          handleHideFormPartial();
        });
      } else {
        console.log('error submit!');
      }
    });
  }
  // 部分支付-上传图片
  import { ElMessage } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';

  import type { UploadProps } from 'element-plus';
  import CardList from '@/views/ordermanagement/Detail/components/cardList.vue';
  const handleAvatarSuccess: UploadProps['onSuccess'] = (
    response,
    uploadFile
  ) => {
    formFormPartial.voucher = URL.createObjectURL(uploadFile.raw!);
  };
  const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (rawFile.type !== 'image/jpeg' && rawFile.type !== 'image/png') {
      ElMessage.error('只允许上传jpg或png格式!');
      return false;
    } else if (rawFile.size / 1024 / 1024 > 5) {
      ElMessage.error('不能超过5MB!');
      return false;
    }
    return true;
  };

  const emit = defineEmits(['update']);
  // 代扣
  const cardListRef = ref();
  function handleShowCardList(id: number) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    cardListRef.value.showCardDialog(id);
  }

  // 结清减免
  let dialogFormSettleVisible = ref(false);
  const formSettleRef = ref<FormInstance>();
  const formSettle = reactive({
    bill_id: 0,
    waive_amount: '',
  });
  function handleShowFormSettle(id: number) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    formSettle.bill_id = id;
    formSettle.waive_amount = '';
    dialogFormSettleVisible.value = true;
  }
  function handleHideFormSettle() {
    formSettle.bill_id = 0;
    formSettle.waive_amount = '';
    dialogFormSettleVisible.value = false;
  }
  function handleFromSettleConfirm(formSettleEf: FormInstance) {
    formSettleEf.validate((valid) => {
      if (valid) {
        waiveBillAmount(
          formSettle.bill_id,
          Number(formSettle.waive_amount)
        ).then((res) => {
          Message.success('修改成功');
          getReastData();
          handleHideFormSettle();
        });
      } else {
        console.log('error submit!');
      }
    });
  }

  // 代扣退款
  let dialogFormRefundVisible = ref(false);
  const formRefundRef = ref<FormInstance>();
  const formRefund = reactive({
    transaction_id: '',
    refund_amount: '',
    refund_reason: '',
    total_refund_amount: 0
  });
  function handleShowRefund(row: any) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    formRefund.total_refund_amount = row.refund_amount || 0;
    formRefund.transaction_id = row.id;
    formRefund.refund_amount = '';
    formRefund.refund_reason = '';
    dialogFormRefundVisible.value = true;
  }
  function handleFromRefundConfirm(formRefundEf: FormInstance) {
    formRefundEf.validate((valid) => {
      if (valid) {
        console.log(formRefund);
        console.log('submit!');
        processRefund({
          transaction_id: formRefund.transaction_id,
          refund_amount: Number(formRefund.refund_amount),
          refund_reason: formRefund.refund_reason
        }).then((res) => {
          Message.success(res.message);
          getReastData();
          dialogFormRefundVisible.value = false;
        });
      } else {
        console.log('error submit!');
      }
    });
  }
  // 退款刷新
  function handleRefundRefresh(row: any) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    row.refundLoading = true;
    refreshRefundStatus({
      transaction_id: row.id
    }).then(res => {
      Message.success('刷新成功');
      getReastData();
    }).finally(() => {
      row.refundLoading = false;
    })
  }
  // 账单状态: 0-待支付；1-已支付；2-逾期已支付；3-逾期待支付；4-已取消；5-已结算；6-已退款；7-部分还款；8-提前结清

  // 状态: 0-待提交； 1-已提交；2-处理成功；3-处理失败；4-已撤回
  function handleGetPaymentStatus( row: any) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    row.loading = true;
    getPaymentStatus(row.transaction_no).then((res) => {
      Message.success('刷新成功');
      getReastData();
    }).finally(() => {
      row.loading = false;
    })
  }

  // 销帐取消
  function writeOffCancel(transaction_no: string) {
    if (!hasOrderPermission()) {
      Message.warning('没有操作当前订单的权限，请在订单分配后操作');
      return false;
    }
    cancelOfflinePayment({ transaction_no }).then((res) => {
      Message.success('刷新成功');
      getReastData();

    });
  }
  function getReastData(isUpdate = true) {
    if(isUpdate) {
      emit('update')
    }
    getBillList();
    pagination.page = 1;
    pagination.pageSize = 10;
    getOrderPayRecordsList();
  }
  defineExpose({
    getReastData
  })


</script>

<template>
  <!--账单信息-->
  <div class="bill-info">
    <div class="title">账单信息（金额单位：元）</div>
    <div class="total-amount-box">

      <el-row>
        <el-col :span="3">
          总金额：<el-tag type="primary" size="default">{{
            total_init.total_repayable_amount
          }}</el-tag>
        </el-col>
        <el-col :span="3">
          前置本金：<el-tag type="primary" size="default">{{
            total_init.pre_principal
          }}</el-tag>
        </el-col>
        <el-col :span="3">
          前置利息：<el-tag type="primary" size="default">{{
            total_init.pre_interest
          }}</el-tag>
        </el-col>
        <el-col :span="3">
          前置担保费：<el-tag type="primary" size="default">{{
            total_init.pre_guarantee_fee
          }}</el-tag>
        </el-col>
      </el-row>
      <!--      <el-table :data="[]" style="width: 100%" empty-text="暂无账单信息">
        <el-table-column type="index" label="No" width="50" />
        <el-table-column prop="user_name" label="总金额" />
        <el-table-column prop="user_name" label="运费"  />
      </el-table>-->
    </div>

    <div class="title">支付情况</div>
    <div class="table">
      <el-table :data="bill_list" style="width: 100%" empty-text="暂无账单信息">
        <el-table-column type="index" label="No" width="50" />
        <el-table-column prop="period_number" label="期数" />
        <el-table-column prop="total_due_amount" label="总金额" />
        <el-table-column prop="total_waive_amount" label="累计减免金额" />
        <el-table-column prop="paid_amount" label="已支付金额" />
        <el-table-column prop="status_text" label="状态"></el-table-column>
        <el-table-column prop="user_name" label="支付方式">
          <template #default="scope">统统付</template>
        </el-table-column>
        <el-table-column prop="payment_time" label="完结时间" />
        <el-table-column prop="due_date" label="还款到期时间" width="120"/>
        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope">
            <el-space v-if="scope.row.status == 0 || scope.row.status == 3 || scope.row.status == 7 || scope.row.status == 9">
              <el-button
                type="primary"
                size="small"
                @click="handleShowFormTime(scope.row.id)"
                >修改时间</el-button
              >
              <el-button
                type="danger"
                size="small"
                @click="handleShowFormSettle(scope.row.id)"
                >结清减免</el-button
              >
              <el-button
                type="danger"
                size="small"
                @click="handleShowFormPartial(scope.row.id)"
                >部分支付</el-button
              >
              <el-button
                type="danger"
                size="small"
                @click="handleShowCardList(scope.row.id)"
                >代扣</el-button
              >
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="title">订单打款记录</div>
    <div class="table">
      <el-table
        :data="disbursement_records"
        style="width: 100%"
        empty-text="暂无订单打款记录"
      >
        <el-table-column type="index" label="No" width="50" />
        <el-table-column prop="status_text" label="状态"></el-table-column>
        <el-table-column prop="error_message" label="错误信息" />
        <el-table-column prop="payment_type" label="支付类型" />
        <el-table-column prop="payout_account" label="支出账户" />
        <el-table-column prop="initiated_at" label="支付发起时间" />
        <el-table-column prop="completed_at" label="支付完成时间" />
        <el-table-column prop="channel_transaction_no" label="支付流水号" />
        <el-table-column prop="amount" label="支付金额" />
      </el-table>
    </div>

    <div class="title">订单支付记录</div>
    <div class="table">
      <el-table
        :data="recordsList"
        style="width: 100%"
        empty-text="暂无订单支付记录"
      >
        <el-table-column type="index" label="No" width="50" />
        <el-table-column prop="status_text" label="状态编号" />
        <!--        <el-table-column prop="image" label="凭证图片" >
          <template #default="scope">
            <el-image style="width: 100px; height: 100px" :src="scope.row.image" :z-index="9999" :preview-src-list="[scope.row.image]" :preview-teleported="true" fit="cover"></el-image>
          </template>
        </el-table-column>-->
        <el-table-column prop="period_number" label="支付期数" />
        <!--        <el-table-column prop="user_name" label="支付方式" />-->
        <el-table-column prop="withhold_type_text" label="支付类型" >
          <template #default="scope">
            {{ scope.row.type == 'PARTIAL_OFFLINE_REPAYMENT'? scope.row.withhold_type_text.replace('统统付','线下'):scope.row.withhold_type_text }}
          </template>
        </el-table-column>
        <!--        <el-table-column prop="user_name" label="支出账户"  />-->
        <el-table-column prop="initiated_at" label="支付发起时间" width="130" />
        <el-table-column prop="completed_at" label="支付完成时间" width="130" />
        <el-table-column
          prop="channel_transaction_no"
          label="支付流水号"
          width="120"
        />
        <el-table-column prop="amount" label="支付金额" />
        <el-table-column
          prop="error_message"
          label="订单响应结果"
          width="130"
        />
        <el-table-column fixed="right" label="操作" width="340">
          <template #default="scope">
            <el-space>
              <el-button
                type="primary"
                size="small"
                @click="handleGetPaymentStatus(scope.row)"
                v-if="scope.row.type != 'REFUND' && scope.row.type != 'PARTIAL_OFFLINE_REPAYMENT'"
                >支付刷新</el-button
              >
              <el-button
                type="danger"
                size="small"
                v-if="scope.row.status == 2 && scope.row.type != 'PARTIAL_OFFLINE_REPAYMENT' && scope.row.type != 'REFUND' && scope.row.refund_amount != scope.row.amount"
                @click="handleShowRefund(scope.row)"
                >代扣退款</el-button
              >
              <el-button
                type="primary"
                v-if="scope.row.type == 'REFUND'"
                size="small"
                @click="handleRefundRefresh(scope.row)"
                :loading="scope.row.refundLoading"
                :disabled="scope.row.refundLoading"
                >退款刷新</el-button
              >
              <el-button
                type="warning"
                size="small"
                v-if="scope.row.type == 'PARTIAL_OFFLINE_REPAYMENT'"
                @click="writeOffCancel(scope.row.transaction_no)"
                >销帐取消</el-button
              >
            </el-space>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="pagination.page"
          :page-sizes="[10, 30, 50, 100, 200, 300, 400]"
          :background="true"
          size="small"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!--  账单时间  -->
    <el-dialog v-model="dialogFormTimeVisible" title="修改账单时间" width="500">
      <el-form :model="formTime" ref="formTimeRef">
        <el-form-item
          label="账单时间"
          label-width="140px"
          prop="due_date"
          :rules="[{ required: true, message: '账单时间必填' }]"
        >
          <el-date-picker
            v-model="formTime.due_date"
            type="date"
            placeholder="请选择账单时间"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormTimeVisible = false">取消</el-button>
          <el-button type="primary" @click="handleFromTimeConfirm(formTimeRef)">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--  部分支付  -->
    <el-dialog
      v-model="dialogFormPartialPayVisible"
      title="部分支付"
      width="500"
    >
      <el-form :model="formFormPartial" ref="formPartialPayRef">
        <el-form-item
          label="支付金额"
          label-width="140px"
          prop="amount"
          :rules="[{ required: true, message: '支付金额必填' }]"
        >
          <el-input
            v-model="formFormPartial.amount"
            type="number"
            placeholder="请输入支付金额"
          />
        </el-form-item>
        <el-form-item
          label="支付渠道"
          label-width="140px"
          prop="payment_channel"
          :rules="[{ required: true, message: '支付渠道必选' }]"
        >
          <el-select
            v-model="formFormPartial.payment_channel"
            placeholder="请选择支付渠道"
          >
            <el-option label="支付宝支付（线下）" value="支付宝支付（线下）" />
            <el-option label="微信支付（线下）" value="微信支付（线下）" />
            <el-option label="银行卡支付（线下）" value="银行卡支付（线下）" />
            <el-option label="信用卡支付（线下）" value="信用卡支付（线下）" />
          </el-select>
        </el-form-item>
        <!--        <el-form-item
          label="凭证"
          label-width="140px"
          prop="voucher"
          :rules="[{ required: true, message: '请上传凭证' }]"
        >
          <el-upload
            class="avatar-uploader"
            action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img
              v-if="formFormPartial.voucher"
              :src="formFormPartial.voucher"
              class="avatar"
            />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>-->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormPartialPayVisible = false"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="handleFormPartialConfirm(formPartialPayRef)"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--  代扣  -->
    <card-list
      ref="cardListRef"
      :order-detail="props.orderDetail"
      @upload-list="getReastData"
    ></card-list>

    <!--  结清减免  -->
    <el-dialog v-model="dialogFormSettleVisible" title="结清减免" width="500">
      <el-form :model="formSettle" ref="formSettleRef">
        <el-form-item
          label="账单金额"
          label-width="140px"
          prop="waive_amount"
          :rules="[{ required: true, message: '账单金额必填' }]"
        >
          <el-input
            v-model="formSettle.waive_amount"
            type="number"
            placeholder="请输入账单金额"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormSettleVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleFromSettleConfirm(formSettleRef)"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!--  代扣退款  -->
    <el-dialog v-model="dialogFormRefundVisible" title="退款" width="500">
      <el-form :model="formRefund" ref="formRefundRef">
        <el-form-item label="已退金额" label-width="140px" prop="refundAmount">
          <el-input
            v-model="formRefund.total_refund_amount"
            disabled
            placeholder="已退金额"
          />
        </el-form-item>
        <el-form-item
          label="退款金额"
          label-width="140px"
          prop="refund_amount"
          :rules="[{ required: true, message: '退款金额必填' }]"
        >
          <el-input
            v-model="formRefund.refund_amount"
            placeholder="请输入账单金额"
          />
        </el-form-item>
        <el-form-item
          label="退款原因"
          label-width="140px"
          prop="refund_reason"
          :rules="[{ required: true, message: '退款' }]"
        >
          <el-input
            type="textarea"
            v-model="formRefund.refund_reason"
            placeholder="请输入退款原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormRefundVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleFromRefundConfirm(formRefundRef)"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped lang="less">
  .title {
    padding-left: 10px;
    position: relative;
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-top: 20px;
    &:after {
      content: '';
      position: absolute;
      background: #409eff;
      width: 3px;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 10px;
    }
  }
  .table {
    overflow: hidden;
    width: 100%;
    margin-top: 15px;
    :deep(.el-table th.el-table__cell) {
      background: #efefef;
    }
    :deep(.el-table thead) {
      color: #333;
    }
  }
  .bill-info {
    width: 100%;
  }
  .total-amount-box {
    padding: 20px 10px;
    font-size: 14px;
    color: #666;
  }
  .avatar-uploader .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .avatar-uploader :deep .el-upload {
    border: 1px dashed #dcdfe6;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: 0.2s;
  }

  .avatar-uploader :deep .el-upload:hover {
    border-color: #409eff;
  }

  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    text-align: center;
  }
  .pagination {
    text-align: right;
    padding: 20px 0;
    display: flex;
    justify-content: end;
  }
</style>