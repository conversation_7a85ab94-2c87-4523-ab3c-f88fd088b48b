{"level":"dev.info","ts":"[2025-08-30 09:39:11.906]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c55272dbf058fd5876","method":"GET","url":"/business/user/get_userinfo","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0401536,"request_size":0,"response_size":725}
{"level":"dev.info","ts":"[2025-08-30 09:39:13.436]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c55f30e788b6d4218c","method":"GET","url":"/business/user/account/get_menu","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":1.358927,"request_size":0,"response_size":7975}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.556]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c5ea2d4a0c0ff4f573","method":"GET","url":"/business/statistics/statisticscontroller/getTrendStatistics","query":"days=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.14715,"request_size":0,"response_size":1298}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.599]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c5ea2d4a0c71a63e32","method":"GET","url":"/business/statistics/statisticscontroller/getHomeStatistics","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1902006,"request_size":0,"response_size":692}
{"level":"dev.info","ts":"[2025-08-30 09:39:14.599]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c5ea2d4a0c9e507302","method":"GET","url":"/business/statistics/statisticscontroller/getHomeStatistics","query":"date_begin=2025-08-30&date_end=2025-08-30&_t=1756517954374","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1902006,"request_size":0,"response_size":680}
{"level":"dev.info","ts":"[2025-08-30 09:39:19.804]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c728311b70e0a90d01","method":"GET","url":"/business/user/get_userinfo","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0591699,"request_size":0,"response_size":725}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.366]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c72e146f38eaa3e00f","method":"GET","url":"/business/user/account/get_menu","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":1.5232381,"request_size":0,"response_size":7975}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.623]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c7946d6a0010683b2b","method":"GET","url":"/business/statistics/statisticscontroller/getTrendStatistics","query":"days=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.062199,"request_size":0,"response_size":1298}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.811]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c79465b10c093053cb","method":"GET","url":"/business/statistics/statisticscontroller/getHomeStatistics","query":"date_begin=2025-08-30&date_end=2025-08-30&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.250415,"request_size":0,"response_size":680}
{"level":"dev.info","ts":"[2025-08-30 09:39:21.841]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c7946d6a0055a9268c","method":"GET","url":"/business/statistics/statisticscontroller/getHomeStatistics","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2806864,"request_size":0,"response_size":692}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.639]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c80ba5e7b414b34487","method":"GET","url":"/business/channel/channelcontroller/getChannelOptions","query":"_t=1756517963492","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0782408,"request_size":0,"response_size":578}
{"level":"dev.info","ts":"[2025-08-30 09:39:23.944]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c80bad9b941128fa06","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.382764,"request_size":25,"response_size":19790}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.903]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c913f9c0741fd32bf3","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756517967979","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":12.9077355,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:39:40.940]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067cc111bb6d4c6ebf415","method":"GET","url":"/business/risk/riskcontroller/getReports","query":"customer_id=7&start_date=&end_date=&_t=1756517978002","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1082574,"request_size":0,"response_size":1355}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.099]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c913f9c07475d8fc75","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":13.1034682,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.163]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067c913f9c07409bd85f8","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756517967980","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":13.1674972,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:39:41.206]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067cc1d6bfae8004e403b","method":"GET","url":"/business/order/manager/getOrderProgress","query":"order_no=******************&_t=1756517981017","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1673187,"request_size":0,"response_size":684}
{"level":"dev.info","ts":"[2025-08-30 09:40:07.603]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d23f6856047023ed37","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2238103,"request_size":25,"response_size":19790}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.410]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d2aad4459c3c6ba18f","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2287558,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.418]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d2aaec4e584d5a46eb","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518009162","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.235903,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:40:09.498]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d2b9a888f847de5f32","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518009413","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0688005,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.569]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d2be50f520376197e9","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=1756518009502","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":20.0605649,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.637]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d59b2c4a4cd4ec99b8","method":"GET","url":"/business/risk/riskcontroller/getReports","query":"customer_id=7&start_date=&end_date=&_t=1756518019516","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":7.8316889,"request_size":0,"response_size":1355}
{"level":"dev.info","ts":"[2025-08-30 09:40:29.676]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067d2aaec4e5868cfff03","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518009162","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":20.4934292,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:41:06.169]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067dfebcb1de8231abba4","method":"GET","url":"/business/user/get_userinfo","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0634558,"request_size":0,"response_size":725}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.252]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067dff7dda42071fb1de4","method":"GET","url":"/business/user/account/get_menu","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.9444113,"request_size":0,"response_size":7975}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.809]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e04e12aad47d73796c","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0547748,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.885]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e04e12aad4b6562c7e","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1310738,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:41:07.886]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e04e0a4ca4416ad983","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1321519,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.046]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e057e1e69c22a07e28","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518067909","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1277286,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.093]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e05ff17618038df4fb","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0388817,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.121]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e062c1ac501c61e96f","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0197992,"request_size":0,"response_size":730}
{"level":"dev.error","ts":"[2025-08-30 09:41:08.163]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e0661656bc4b1061c8","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/bcb09dee6696b9884216feea25140c10_20250818170742.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.005355,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-08-30 09:41:08.163]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e0662e19283eb67b2f","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/885cf6a8fd6d0bfd20953e0f77ee10d8_20250818170747.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.004304,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.208]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e0662e1928f231e65d","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0490616,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.221]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e0662e1928324e403d","method":"GET","url":"/business/risk/riskcontroller/getReports","query":"customer_id=7&start_date=&end_date=&_t=1756518068149","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0627549,"request_size":0,"response_size":1355}
{"level":"dev.info","ts":"[2025-08-30 09:41:08.378]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e06e3dc5f0f11f4dc9","method":"GET","url":"/business/order/manager/getOrderProgress","query":"order_no=******************&_t=1756518068282","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.084265,"request_size":0,"response_size":684}
{"level":"dev.info","ts":"[2025-08-30 09:41:11.832]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e13f1fa030d8ae3abf","method":"GET","url":"/business/user/get_userinfo","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0335976,"request_size":0,"response_size":725}
{"level":"dev.info","ts":"[2025-08-30 09:41:12.820]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e142b76f483254c934","method":"GET","url":"/business/user/account/get_menu","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.9611124,"request_size":0,"response_size":7975}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.350]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e18f177a549860ab3e","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2095633,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.376]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e18f177a54b47981cb","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2359427,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.425]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e18f177a54bfc077a4","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2852166,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.542]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e1a0dbbb101ce19ce4","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518073428","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1040308,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.632]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e1a7946d80dfc3d49d","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.080583,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.669]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e1accc8a6c9f7b045e","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0309598,"request_size":0,"response_size":730}
{"level":"dev.error","ts":"[2025-08-30 09:41:13.691]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e1afac57a8e5809e04","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/bcb09dee6696b9884216feea25140c10_20250818170742.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0047526,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-08-30 09:41:13.691]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e1afac57a87169b186","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/885cf6a8fd6d0bfd20953e0f77ee10d8_20250818170747.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0042442,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.720]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e1af8f67d84e2ec445","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0348884,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.748]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e1afac57a8fcc36ea3","method":"GET","url":"/business/risk/riskcontroller/getReports","query":"customer_id=7&start_date=&end_date=&_t=1756518073679","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0612842,"request_size":0,"response_size":1355}
{"level":"dev.info","ts":"[2025-08-30 09:41:13.862]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e1b55c1724233486b3","method":"GET","url":"/business/order/manager/getOrderProgress","query":"order_no=******************&_t=1756518073775","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.080114,"request_size":0,"response_size":684}
{"level":"dev.info","ts":"[2025-08-30 09:41:17.380]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e289cfcd707c54f12a","method":"GET","url":"/business/user/get_userinfo","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.032315,"request_size":0,"response_size":725}
{"level":"dev.info","ts":"[2025-08-30 09:41:18.698]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e28d7a30c843d3a9b4","method":"GET","url":"/business/user/account/get_menu","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":1.2904844,"request_size":0,"response_size":7975}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.029]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2eba78da8f4f9c1fd","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0411427,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.070]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2ebaf3ecc5c4428de","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0813838,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.072]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2eba78da872223662","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0839195,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.198]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2f2368804557f2a1f","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518079092","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.100268,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.270]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2f9779c20ca438452","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0500671,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.296]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2fd02653c8125e1ce","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0167659,"request_size":0,"response_size":730}
{"level":"dev.error","ts":"[2025-08-30 09:41:19.316]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e2fee9d40ce8461e2d","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/bcb09dee6696b9884216feea25140c10_20250818170742.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0051596,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-08-30 09:41:19.322]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e2ff5d1c28309d02de","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/885cf6a8fd6d0bfd20953e0f77ee10d8_20250818170747.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0037554,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.389]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2ff2050e0aec9d3ab","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0747891,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.420]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e2ff4c42183c6398b8","method":"GET","url":"/business/risk/riskcontroller/getReports","query":"customer_id=7&start_date=&end_date=&_t=1756518079306","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1026157,"request_size":0,"response_size":1355}
{"level":"dev.info","ts":"[2025-08-30 09:41:19.545]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e3075f8dd42d335522","method":"GET","url":"/business/order/manager/getOrderProgress","query":"order_no=******************&_t=1756518079445","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0920302,"request_size":0,"response_size":684}
{"level":"dev.info","ts":"[2025-08-30 09:41:29.262]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e54db1f6a8a7adbc44","method":"GET","url":"/business/user/get_userinfo","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0375092,"request_size":0,"response_size":725}
{"level":"dev.info","ts":"[2025-08-30 09:41:30.564]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e55255a650b956ce2c","method":"GET","url":"/business/user/account/get_menu","query":"_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":1.2637881,"request_size":0,"response_size":7975}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.209]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e5b266bc64e6233c1b","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":6.2967551,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.251]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e5b266bc640f858037","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":6.338098,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.257]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e5b266bc64f6c7632a","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":6.3446042,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.336]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e72da440305dfa2ef9","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518097254","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0605546,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.403]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e731aaf584bab24da6","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0608719,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.439]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e736328cfcc4fba995","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0198317,"request_size":0,"response_size":730}
{"level":"dev.error","ts":"[2025-08-30 09:41:37.489]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e739c857481eb4bc94","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/885cf6a8fd6d0bfd20953e0f77ee10d8_20250818170747.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0101842,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.error","ts":"[2025-08-30 09:41:37.491]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"186067e739d080bc9d3fe837","method":"GET","url":"/business/http://************:8108/https://fincore.oss-cn-heyuan.aliyuncs.com/uploads/bcb09dee6696b9884216feea25140c10_20250818170742.jpg","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":500,"response_time":0.0122749,"request_size":0,"response_size":83,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.serveError\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:652\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:645\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.515]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e739c01bdcf6d4fe53","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.036687,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.544]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e739c85748e2cb82ad","method":"GET","url":"/business/risk/riskcontroller/getReports","query":"customer_id=7&start_date=&end_date=&_t=1756518097465","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0640797,"request_size":0,"response_size":1355}
{"level":"dev.info","ts":"[2025-08-30 09:41:37.670]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186067e741af60f0e4995430","method":"GET","url":"/business/order/manager/getOrderProgress","query":"order_no=******************&_t=1756518097597","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0587054,"request_size":0,"response_size":684}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.700]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686c6a9b960cab3b3484","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518669001","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.1706622,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.678]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686cece04fe0b440cf04","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.9639064,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.679]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686ced0de63062eff139","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518671706","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.9619671,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.739]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686ced0de630dc10c92a","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518671706","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":3.0223556,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.852]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686d9e976610dc644f9b","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518674681","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.15656,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:51:14.990]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686da8a36b2c8ee14037","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1256763,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:51:15.022]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686db0920d5c3a5f601f","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0246022,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:51:15.110]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860686db2d1c10c1d40a6fc","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0755126,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.053]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688184fc5db453dcc3ed","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518760138","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":7.8832247,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.745]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068835bda405c04dabb40","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.6798354,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.765]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068835beab8602ca50f64","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518768059","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.6984303,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.773]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068835beab86044d89376","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518768059","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.7071618,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.832]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606883fc395718d3861475","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518770747","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0759602,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.908]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606884019f77b47a964ee4","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0609579,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.938]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688405ff72f07848dfc5","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0180992,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:52:50.985]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688407dd4a485e59463c","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0341023,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.721]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688d5c9302f40e3de183","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518807881","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":4.6940925,"request_size":0,"response_size":120}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.832]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688c9127c53c93a3a066","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518807606","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":8.2184541,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.634]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688e7bf02c3474ba118e","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518815836","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":7.7863119,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.774]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688e7bf02c34a13fd35b","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518815836","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":7.9264335,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.875]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860688e7bbdd0ccfc0d72a4","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":8.0298987,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:53:43.953]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068905b1b11fc28f1ba4b","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518823878","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.065618,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.016]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068905f82ca14bd75745a","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0545442,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.052]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068906346d1186be96842","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0278156,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:53:44.132]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689065f7f3d8be045162","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0628653,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.563]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068916c39da300f41c472","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518828456","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.0941293,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.771]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606891ea195c8c5436adc5","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.190033,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.782]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606891ea31cdf822a2fe82","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518830571","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1991656,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.818]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606891ea435578a7a9d03c","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518830571","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2343136,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.856]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606891f68f2618fedc5a33","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518830777","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0657101,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.939]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606891faeb27c03043f5c1","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0759871,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.967]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606891fff874e8602ca710","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0186045,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:53:51.018]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068920201cdc0ed7ffc83","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0358596,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.629]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689182216124601de79e","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518828832","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":3.7920668,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.713]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606892650dedb8e5d02f7d","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518832633","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0689085,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.749]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606892650dedb88473c34f","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518832633","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.105409,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.750]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689264dfab8804e16e8e","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1089472,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.845]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068926c58930c7c3b8734","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518832760","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0784996,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.936]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068927168ab70ff044555","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0849713,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.994]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689276fda824313d43f1","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0489382,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:53:53.057]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068927ab38b3cc4783b6b","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0500099,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.957]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068935fcd59a0dca14e85","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518836841","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.1063101,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.036]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893de4f31cc9c08e1be","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518838963","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0618697,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.131]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893de5ea8006affad1b","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518838963","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.156846,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.131]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893de2f3b60e1549631","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.159954,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.207]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893e8948d080cca4436","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518839135","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0617177,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.337]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893ece264e8924f7da6","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1194257,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.370]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893f49d45e0d9c82b28","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0221218,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.421]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606893f6845e5c3e700bb4","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0404343,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.076]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068937a914ae4759fec92","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518837294","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":3.7757924,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.194]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068945ca88b2c79f7191a","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518841082","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1007613,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.264]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068945c87f5c4cef85acd","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1724942,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.265]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068945cc2820ccee38a39","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518841082","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1698569,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.340]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606894679241cccc229e70","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518841267","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0641869,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.435]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068946bc908349b9f8c89","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0881532,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.463]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606894719ca93cf3b3c3fc","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0185591,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.517]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606894734a68dc6323febe","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0446311,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.103]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606894cd5e9078f9fc6e63","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518842974","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.1188451,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.248]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068954caf4bec9f4fdc25","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518845110","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1283653,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.313]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068954c86c80c1f8988c2","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1957968,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.314]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068954cb6fea06ddfdb44","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518845111","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1931748,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.521]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689558ea91004ba62697","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518845317","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1959595,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.639]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895654ce54c1f2242d7","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1058159,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.682]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068956c24a940d44093f2","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0343921,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.837]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"186068956eccc128c5e1190e","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1443414,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.241]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606894d51b524c222fa41d","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518843109","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":4.1270042,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.324]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895cc26f2a8746817bd","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518847247","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0655432,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.379]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895cbf033082a71d7bc","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1241204,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.388]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895cc26f2a861725753","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518847247","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1297775,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.527]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895d417de64f154e5e8","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518847383","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1354554,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.584]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895dcd1c29021d6d71a","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0454877,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.617]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895e00b38ec70faa1ba","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.025066,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.680]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"18606895e2100f78fdecb6f8","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0529059,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:54:31.474]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689af27bcfb457a1d26f","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518868860","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.0968647,"request_size":0,"response_size":120}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.285]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689adc3e00dcda47a189","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518868998","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":3.2810749,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.447]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689ba0b9fcf466599b8e","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518872291","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1468287,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.550]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689ba0c1dfb446f812a3","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518872291","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.249292,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.556]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689ba0a95ea8858adaf8","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2569306,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.683]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689bb1907120a9268633","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518872571","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1005566,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.761]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689bb820e9e8fa36d8b0","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0688627,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.789]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689bbcb000e882a4c3c6","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0199494,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.842]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689bbeb54024665dd005","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0390414,"request_size":39,"response_size":502}
{"level":"dev.info","ts":"[2025-08-30 09:54:40.259]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689d00ae09d85f2f5bef","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518878199","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.0548185,"request_size":0,"response_size":120}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.222]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689cf87e92c8abffd3da","method":"GET","url":"/business/repayment/manager/getPaymentStatus","query":"transaction_no=RP1756257020147545703&_t=1756518878056","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":3.1551893,"request_size":0,"response_size":514}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.331]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689db5a05f08c906a54c","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=******************&_t=1756518881228","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0905995,"request_size":0,"response_size":1472}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.438]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689db5be7c0411c6d4bf","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"page=1&pageSize=10&order_no=******************&_t=1756518881229","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1959095,"request_size":0,"response_size":3176}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.449]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689db57c566caafb971f","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.2113628,"request_size":56,"response_size":1440}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.539]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689dc2be7eb8aaa2ef6a","method":"GET","url":"/business/order/manager/getOrderCustomerInfo","query":"orderId=89&_t=1756518881453","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0781316,"request_size":0,"response_size":1515}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.591]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689dc7e20428487dddfc","method":"GET","url":"/business/customer/customercontroller/getCustomerDetail","query":"id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0440707,"request_size":0,"response_size":1779}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.618]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689dcafa7820e753e6fc","method":"GET","url":"/business/bankcard/bankcardcontroller/getBankCardList","query":"customer_id=7&_t=*************","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0194833,"request_size":0,"response_size":730}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.691]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1860689dcccd1e3c42fe0b71","method":"POST","url":"/business/order/manager/listCollectionOfOrder","query":"","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.061449,"request_size":39,"response_size":502}
