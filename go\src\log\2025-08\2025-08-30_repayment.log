{"level":"dev.debug","ts":"[2025-08-30 09:51:09.841]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"722166d546c26ad8:28064:1756518669826511800","action":"redis_lock_start","expiration":30,"timeout":30}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.666]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"722166d546c26ad8:28064:1756518669826511800","action":"redis_lock_success","duration":1.8252049}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.666]","caller":"repayment/service.go:171","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.691]","caller":"repayment/service.go:190","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.691]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"722166d546c26ad8:28064:1756518669826511800","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:51:11.699]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"722166d546c26ad8:28064:1756518669826511800","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:52:40.305]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"9b12d6fa308092ff:1052:1756518760289340600","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:52:46.001]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"9b12d6fa308092ff:1052:1756518760289340600","action":"redis_lock_success","duration":5.6959364}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.001]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.039]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.039]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"9b12d6fa308092ff:1052:1756518760289340600","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:52:48.052]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"9b12d6fa308092ff:1052:1756518760289340600","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:53:27.663]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"cb038980fc945c6b:1052:1756518807643444500","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:53:31.026]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"cb038980fc945c6b:1052:1756518807643444500","action":"redis_lock_success","duration":3.3631911}
{"level":"dev.debug","ts":"[2025-08-30 09:53:31.043]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"9005b141f10bb05a:1052:1756518811032180700","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:53:33.026]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.warn","ts":"[2025-08-30 09:53:33.063]","caller":"lock/redis_lock.go:173","msg":"Redis加锁超时或被取消","key":"lock:payment_refresh_lock_RP1756257020147545703","reason":"context deadline exceeded"}
{"level":"dev.warn","ts":"[2025-08-30 09:53:35.720]","caller":"lock/redis_lock.go:114","msg":"Redis加锁失败","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"9005b141f10bb05a:1052:1756518811032180700","action":"redis_lock_failed","duration":4.6775231}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.721]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.721]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"cb038980fc945c6b:1052:1756518807643444500","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:35.832]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"cb038980fc945c6b:1052:1756518807643444500","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:53:48.494]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"76a6355ce1cc14df:1052:1756518828476495600","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:53:48.511]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"76a6355ce1cc14df:1052:1756518828476495600","action":"redis_lock_success","duration":0.0166019}
{"level":"dev.debug","ts":"[2025-08-30 09:53:48.852]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"7abffd1a65105b43:1052:1756518828843000600","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.511]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.557]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.557]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"76a6355ce1cc14df:1052:1756518828476495600","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.563]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"76a6355ce1cc14df:1052:1756518828476495600","action":"redis_unlock_result","success":true}
{"level":"dev.info","ts":"[2025-08-30 09:53:50.584]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"7abffd1a65105b43:1052:1756518828843000600","action":"redis_lock_success","duration":1.7315805}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.584]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.619]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.620]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"7abffd1a65105b43:1052:1756518828843000600","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:52.628]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"7abffd1a65105b43:1052:1756518828843000600","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:53:56.872]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"2c7f6a9d4a5ef7c8:1052:1756518836861689500","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:53:56.887]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"2c7f6a9d4a5ef7c8:1052:1756518836861689500","action":"redis_lock_success","duration":0.0155735}
{"level":"dev.debug","ts":"[2025-08-30 09:53:57.315]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"f7f5e92467d0e30c:1052:1756518837305443200","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.887]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.940]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.940]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"2c7f6a9d4a5ef7c8:1052:1756518836861689500","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:53:58.957]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"2c7f6a9d4a5ef7c8:1052:1756518836861689500","action":"redis_unlock_result","success":true}
{"level":"dev.info","ts":"[2025-08-30 09:53:59.036]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"f7f5e92467d0e30c:1052:1756518837305443200","action":"redis_lock_success","duration":1.7209031}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.036]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.067]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.067]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"f7f5e92467d0e30c:1052:1756518837305443200","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:01.074]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"f7f5e92467d0e30c:1052:1756518837305443200","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:54:03.045]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"32763f463ea6d0f2:1052:1756518843018754300","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:54:03.054]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"32763f463ea6d0f2:1052:1756518843018754300","action":"redis_lock_success","duration":0.0094881}
{"level":"dev.debug","ts":"[2025-08-30 09:54:03.136]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"74710adfde237eb7:1052:1756518843119848100","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.054]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.096]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.096]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"32763f463ea6d0f2:1052:1756518843018754300","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.098]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"32763f463ea6d0f2:1052:1756518843018754300","action":"redis_unlock_result","success":true}
{"level":"dev.info","ts":"[2025-08-30 09:54:05.188]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"74710adfde237eb7:1052:1756518843119848100","action":"redis_lock_success","duration":2.0523616}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.188]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.231]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.231]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"74710adfde237eb7:1052:1756518843119848100","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:07.238]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"74710adfde237eb7:1052:1756518843119848100","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:54:29.177]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"ead680239ae91c8c:4584:1756518869167052000","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:54:29.197]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"ead680239ae91c8c:4584:1756518869167052000","action":"redis_lock_success","duration":0.0196779}
{"level":"dev.debug","ts":"[2025-08-30 09:54:29.428]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"67a39f45795ce452:4584:1756518869412019400","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.warn","ts":"[2025-08-30 09:54:31.473]","caller":"lock/redis_lock.go:173","msg":"Redis加锁超时或被取消","key":"lock:payment_refresh_lock_RP1756257020147545703","reason":"context deadline exceeded"}
{"level":"dev.warn","ts":"[2025-08-30 09:54:31.473]","caller":"lock/redis_lock.go:114","msg":"Redis加锁失败","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"67a39f45795ce452:4584:1756518869412019400","action":"redis_lock_failed","duration":2.0454919}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.197]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.264]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.264]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"ead680239ae91c8c:4584:1756518869167052000","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:32.284]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"ead680239ae91c8c:4584:1756518869167052000","action":"redis_unlock_result","success":true}
{"level":"dev.debug","ts":"[2025-08-30 09:54:38.093]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"93b2142b0495db94:4584:1756518878080785100","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.info","ts":"[2025-08-30 09:54:38.114]","caller":"lock/redis_lock.go:100","msg":"Redis加锁成功","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"93b2142b0495db94:4584:1756518878080785100","action":"redis_lock_success","duration":0.0213292}
{"level":"dev.debug","ts":"[2025-08-30 09:54:38.236]","caller":"lock/redis_lock.go:77","msg":"开始Redis加锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"66beeb198fa549ec:4584:1756518878226521900","action":"redis_lock_start","expiration":30,"timeout":2}
{"level":"dev.warn","ts":"[2025-08-30 09:54:40.256]","caller":"lock/redis_lock.go:173","msg":"Redis加锁超时或被取消","key":"lock:payment_refresh_lock_RP1756257020147545703","reason":"context deadline exceeded"}
{"level":"dev.warn","ts":"[2025-08-30 09:54:40.259]","caller":"lock/redis_lock.go:114","msg":"Redis加锁失败","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"66beeb198fa549ec:4584:1756518878226521900","action":"redis_lock_failed","duration":2.0233316}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.115]","caller":"repayment/service.go:173","msg":"开始查询支付状态","transaction_no":"RP1756257020147545703","action":"query_payment_status_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.210]","caller":"repayment/service.go:192","msg":"交易非已提交状态，返回本地状态","transaction_no":"RP1756257020147545703","status":3,"action":"return_local_status"}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.210]","caller":"lock/redis_lock.go:208","msg":"开始Redis解锁","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"93b2142b0495db94:4584:1756518878080785100","action":"redis_unlock_start"}
{"level":"dev.info","ts":"[2025-08-30 09:54:41.221]","caller":"lock/redis_lock.go:225","msg":"Redis解锁完成","key":"lock:payment_refresh_lock_RP1756257020147545703","value":"93b2142b0495db94:4584:1756518878080785100","action":"redis_unlock_result","success":true}
